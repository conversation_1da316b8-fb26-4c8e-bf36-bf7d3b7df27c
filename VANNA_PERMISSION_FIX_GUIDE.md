# 🔧 Vanna 权限问题修复指南

## 🎯 问题描述

错误信息：
```
The LLM is not allowed to see the data in your database. Your question requires database introspection to generate the necessary SQL. Please set allow_llm_to_see_data=True to enable this.
```

## 🔍 问题原因

1. **权限设置问题**: Vanna 的 `allow_llm_to_see_data` 权限没有正确设置
2. **环境变量缺失**: 数据库连接或 API 密钥环境变量没有设置
3. **Vanna 模型未训练**: 新的 Vanna 实例需要先训练才能使用

## ✅ 解决方案

### 1. 设置环境变量

创建或编辑 `.env` 文件，添加以下配置：

```bash
# 数据库配置
MYSQL_HOST=localhost
MYSQL_USER=root
MYSQL_PASSWORD=your_password
MYSQL_DATABASE=tiktok_data
MYSQL_PORT=3306

# OpenAI 配置 (本地模式)
OPENAI_API_KEY=your_openai_api_key
OPENAI_BASE_URL=https://api.openai.com/v1

# 或者 Vanna 云端配置
VANNA_API_KEY=your_vanna_api_key
VANNA_MODEL=your_vanna_model_name
```

### 2. 权限设置修复

我已经在代码中添加了多重权限设置机制：

```python
# 在 DatabaseQueryTool 中
try:
    _global_vn_instance.allow_llm_to_see_data = True
    logger.info("设置 allow_llm_to_see_data = True")
except Exception as e:
    logger.warning(f"无法设置 allow_llm_to_see_data: {e}")

# 如果有config属性，也尝试设置
try:
    if hasattr(_global_vn_instance, 'config') and _global_vn_instance.config is not None:
        if hasattr(_global_vn_instance.config, 'allow_llm_to_see_data'):
            _global_vn_instance.config.allow_llm_to_see_data = True
        elif isinstance(_global_vn_instance.config, dict):
            _global_vn_instance.config['allow_llm_to_see_data'] = True
except Exception as e:
    logger.warning(f"无法通过config设置权限: {e}")
```

### 3. 训练 Vanna 模型

如果是新的 Vanna 实例，需要先训练：

```bash
python train_vanna.py
```

### 4. 测试修复效果

运行测试脚本验证修复：

```bash
python test_vanna_simple.py
```

## 🔧 手动修复步骤

### 步骤 1: 检查环境变量

```bash
# Windows
echo %MYSQL_HOST%
echo %OPENAI_API_KEY%

# Linux/Mac
echo $MYSQL_HOST
echo $OPENAI_API_KEY
```

### 步骤 2: 设置环境变量

**Windows (PowerShell):**
```powershell
$env:MYSQL_HOST="localhost"
$env:MYSQL_USER="root"
$env:MYSQL_PASSWORD="your_password"
$env:MYSQL_DATABASE="tiktok_data"
$env:OPENAI_API_KEY="your_openai_api_key"
```

**Linux/Mac:**
```bash
export MYSQL_HOST="localhost"
export MYSQL_USER="root"
export MYSQL_PASSWORD="your_password"
export MYSQL_DATABASE="tiktok_data"
export OPENAI_API_KEY="your_openai_api_key"
```

### 步骤 3: 重启应用

```bash
chainlit run app.py
```

## 🧪 验证修复

### 测试 1: 基本连接测试

```python
from src.tools.database_tools import DatabaseConnectionTool

tool = DatabaseConnectionTool()
result = tool._run("")
print(result)
```

### 测试 2: 查询测试

```python
from src.tools.database_tools import DatabaseQueryTool

tool = DatabaseQueryTool()
result = tool._run("查询数据库中有多少个表")
print(result)
```

### 测试 3: 完整功能测试

在 Chainlit 应用中输入：
- "查询粉丝数最多的达人"
- "统计各地区达人数量"
- "显示播放量超过100万的作品"

## 🚨 常见问题

### 问题 1: 环境变量不生效

**解决方案**: 
- 确保 `.env` 文件在项目根目录
- 重启终端和应用
- 检查 `.env` 文件格式（没有空格，正确的等号）

### 问题 2: 数据库连接失败

**解决方案**:
- 检查数据库服务是否启动
- 验证用户名密码是否正确
- 确认数据库名称存在

### 问题 3: OpenAI API 调用失败

**解决方案**:
- 验证 API 密钥是否有效
- 检查网络连接
- 确认 API 配额是否充足

### 问题 4: Vanna 模型未训练

**解决方案**:
```bash
# 训练 Vanna 模型
python train_vanna.py

# 检查训练状态
python check_vanna_config.py
```

## 📋 完整的 .env 文件示例

```bash
# 数据库配置
MYSQL_HOST=localhost
MYSQL_USER=root
MYSQL_PASSWORD=123456
MYSQL_DATABASE=tiktok_data
MYSQL_PORT=3306
MYSQL_CONNECTION_TIMEOUT=60
MYSQL_POOL_SIZE=5

# OpenAI 配置
OPENAI_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
OPENAI_BASE_URL=https://api.openai.com/v1

# Vanna 配置 (可选，如果使用云端模式)
VANNA_API_KEY=vn-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
VANNA_MODEL=your-model-name

# 其他配置
LOG_LEVEL=INFO
```

## 🎯 预期结果

修复成功后，您应该能看到：

1. **正常的 SQL 生成**:
```sql
SELECT unique_id, author_name, follower_count 
FROM at_tiktok_author_pool 
WHERE is_del = 0 
ORDER BY follower_count DESC 
LIMIT 10;
```

2. **结构化的数据返回**:
```markdown
## 📊 查询结果

**查询问题:** 查询粉丝数最多的达人
**查询意图:** 达人排行
**数据总数:** 10 条

| 用户名 | 达人昵称 | 粉丝数 | 总获赞数 | 认证状态 |
|--------|----------|--------|----------|----------|
| user1  | 达人A    | 1,234,567 | 5,678,901 | 已认证 |
```

3. **日志信息**:
```
2025-07-25 17:45:00 - 设置 allow_llm_to_see_data = True
2025-07-25 17:45:00 - Vanna LLM 数据库内省权限: True
2025-07-25 17:45:00 - 已启用 Vanna LLM 数据库内省功能
```

## 🆘 如果仍然有问题

1. **查看日志**: 检查应用启动时的日志信息
2. **联系支持**: 提供完整的错误信息和环境配置
3. **重新训练**: 删除现有的 Vanna 模型文件，重新训练

记住：修复后需要重启 Chainlit 应用才能生效！
