"""
团队组装模块 - 组装 Agents 和 Tasks，创建 Crew
"""

from crewai import Crew, Process
from .agents import (
    create_data_analyst_agent,
    create_report_writer_agent,
    create_data_validator_agent,
)
from .tasks import create_comprehensive_analysis_task


def create_tiktok_crew(
    topic: str, use_validation: bool = False, model_provider: str = "auto"
):
    """
    创建 TikTok 数据分析团队

    Args:
        topic (str): 分析主题
        use_validation (bool): 是否使用数据验证智能体
        model_provider (str): 模型提供商 ('openai', 'tongyi', 'auto')

    Returns:
        Crew: 配置好的 CrewAI 团队
    """
    # 创建智能体
    data_analyst = create_data_analyst_agent(model_provider)
    report_writer = create_report_writer_agent(model_provider)

    # 可选的数据验证智能体
    data_validator = None
    if use_validation:
        data_validator = create_data_validator_agent(model_provider)

    # 创建任务
    tasks = create_comprehensive_analysis_task(
        topic=topic,
        data_agent=data_analyst,
        report_agent=report_writer,
        validation_agent=data_validator,
    )

    # 组装智能体列表
    agents = [data_analyst, report_writer]
    if data_validator:
        agents.append(data_validator)

    # 创建团队
    crew = Crew(
        agents=agents,
        tasks=tasks,
        process=Process.sequential,  # 顺序执行任务
        verbose=True,  # 详细输出
        memory=True,  # 启用记忆功能
        embedder={"provider": "openai", "config": {"model": "text-embedding-3-small"}},
    )

    return crew


def create_simple_crew(topic: str, model_provider: str = "auto"):
    """
    创建简化版的 TikTok 数据分析团队（仅包含核心智能体）

    Args:
        topic (str): 分析主题
        model_provider (str): 模型提供商

    Returns:
        Crew: 配置好的简化 CrewAI 团队
    """
    return create_tiktok_crew(
        topic, use_validation=False, model_provider=model_provider
    )


def create_enhanced_crew(topic: str, model_provider: str = "auto"):
    """
    创建增强版的 TikTok 数据分析团队（包含数据验证）

    Args:
        topic (str): 分析主题
        model_provider (str): 模型提供商

    Returns:
        Crew: 配置好的增强 CrewAI 团队
    """
    return create_tiktok_crew(topic, use_validation=True, model_provider=model_provider)


class CrewManager:
    """团队管理器 - 管理多个分析任务"""

    def __init__(self):
        self.active_crews = {}
        self.completed_tasks = []

    def create_crew_for_topic(self, topic: str, crew_type: str = "simple"):
        """
        为特定主题创建团队

        Args:
            topic (str): 分析主题
            crew_type (str): 团队类型 ("simple" 或 "enhanced")

        Returns:
            Crew: 创建的团队
        """
        if crew_type == "enhanced":
            crew = create_enhanced_crew(topic)
        else:
            crew = create_simple_crew(topic)

        # 存储活跃的团队
        crew_id = f"{crew_type}_{len(self.active_crews)}"
        self.active_crews[crew_id] = {
            "crew": crew,
            "topic": topic,
            "type": crew_type,
            "status": "created",
        }

        return crew, crew_id

    def execute_crew(self, crew_id: str):
        """
        执行指定的团队任务

        Args:
            crew_id (str): 团队ID

        Returns:
            str: 执行结果
        """
        if crew_id not in self.active_crews:
            return "团队不存在"

        crew_info = self.active_crews[crew_id]
        crew = crew_info["crew"]

        try:
            # 更新状态
            crew_info["status"] = "running"

            # 执行任务
            result = crew.kickoff()

            # 更新状态和结果
            crew_info["status"] = "completed"
            crew_info["result"] = result

            # 移动到已完成任务
            self.completed_tasks.append(crew_info)
            del self.active_crews[crew_id]

            return result

        except Exception as e:
            crew_info["status"] = "failed"
            crew_info["error"] = str(e)
            return f"任务执行失败: {str(e)}"

    def get_crew_status(self, crew_id: str):
        """获取团队状态"""
        if crew_id in self.active_crews:
            return self.active_crews[crew_id]["status"]

        # 检查已完成的任务
        for task in self.completed_tasks:
            if task.get("id") == crew_id:
                return task["status"]

        return "not_found"
