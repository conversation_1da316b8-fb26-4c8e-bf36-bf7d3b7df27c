"""
数据增强器模块
根据用户查询意图，自动补充相关的基本信息字段，提供更完整的数据返回
"""

import json
import os
import re
from typing import Dict, List, Any, Optional, Tuple
import logging

logger = logging.getLogger(__name__)


class DataEnhancer:
    """数据增强器 - 根据查询意图自动补充相关字段"""
    
    def __init__(self):
        """初始化数据增强器"""
        self.config = self._load_config()
        self.query_patterns = self._build_query_patterns()
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            config_path = os.path.join(os.path.dirname(__file__), '..', 'config', 'field_mappings.json')
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return {}
    
    def _build_query_patterns(self) -> Dict[str, List[str]]:
        """构建查询模式匹配规则"""
        return {
            "达人排行": [
                r"粉丝.*最多", r"粉丝.*排行", r"粉丝.*前\d+", r"粉丝.*top\d*",
                r"获赞.*最多", r"获赞.*排行", r"点赞.*最多", r"点赞.*排行",
                r"达人.*排行", r"博主.*排行", r"用户.*排行",
                r"最受欢迎.*达人", r"热门.*达人", r"知名.*达人"
            ],
            "作品排行": [
                r"播放.*最多", r"播放.*排行", r"播放.*前\d+", r"播放.*top\d*",
                r"热门.*作品", r"热门.*视频", r"爆款.*作品", r"爆款.*视频",
                r"作品.*排行", r"视频.*排行", r"内容.*排行",
                r"互动.*最高", r"互动.*最多", r"参与度.*最高"
            ],
            "互动分析": [
                r"互动率", r"参与度", r"点赞率", r"评论率", r"分享率",
                r"互动.*分析", r"互动.*统计", r"互动.*数据",
                r"用户.*参与", r"粉丝.*互动", r"观众.*反应"
            ],
            "电商分析": [
                r"电商", r"带货", r"商品", r"卖家", r"小店",
                r"电商.*达人", r"带货.*达人", r"商业.*达人",
                r"销售.*数据", r"商品.*推广", r"电商.*转化"
            ],
            "地区分析": [
                r"地区", r"区域", r"国家", r"城市", r"位置",
                r"地区.*分布", r"区域.*统计", r"地理.*分析",
                r"各地.*达人", r"不同.*地区", r"海外.*达人"
            ],
            "时间分析": [
                r"上周", r"本周", r"上月", r"本月", r"最近.*天", r"最近.*周", r"最近.*月",
                r"趋势", r"增长", r"变化", r"发展",
                r"时间.*分析", r"时间.*统计", r"历史.*数据"
            ]
        }
    
    def analyze_query_intent(self, query: str) -> Tuple[str, List[str]]:
        """
        分析查询意图，确定需要增强的数据类型
        
        Args:
            query: 用户查询语句
            
        Returns:
            Tuple[增强类型, 匹配的关键词列表]
        """
        query_lower = query.lower()
        matched_types = []
        matched_keywords = []
        
        # 遍历所有查询模式
        for query_type, patterns in self.query_patterns.items():
            for pattern in patterns:
                if re.search(pattern, query_lower):
                    matched_types.append(query_type)
                    matched_keywords.append(pattern)
                    break
        
        # 如果匹配多个类型，选择最相关的
        if matched_types:
            primary_type = self._select_primary_type(matched_types, query_lower)
            return primary_type, matched_keywords
        
        # 默认返回通用增强
        return "通用查询", []
    
    def _select_primary_type(self, matched_types: List[str], query: str) -> str:
        """选择主要的查询类型"""
        # 优先级排序
        priority_order = ["达人排行", "作品排行", "互动分析", "电商分析", "地区分析", "时间分析"]
        
        for priority_type in priority_order:
            if priority_type in matched_types:
                return priority_type
        
        return matched_types[0] if matched_types else "通用查询"
    
    def enhance_sql_query(self, original_sql: str, query_intent: str) -> str:
        """
        根据查询意图增强SQL查询，自动添加相关字段
        
        Args:
            original_sql: 原始SQL查询
            query_intent: 查询意图类型
            
        Returns:
            增强后的SQL查询
        """
        try:
            # 获取查询模板配置
            templates = self.config.get("query_templates", {})
            
            if query_intent in templates:
                template = templates[query_intent]
                enhanced_sql = self._apply_template_enhancement(original_sql, template)
                return enhanced_sql
            
            return original_sql
            
        except Exception as e:
            logger.error(f"SQL增强失败: {e}")
            return original_sql
    
    def _apply_template_enhancement(self, sql: str, template: Dict[str, Any]) -> str:
        """应用模板增强SQL"""
        try:
            # 解析SQL结构
            sql_upper = sql.upper()
            
            # 检查是否已经包含必要字段
            auto_fields = template.get("auto_fields", [])
            missing_fields = []
            
            for field in auto_fields:
                if field.lower() not in sql.lower():
                    missing_fields.append(field)
            
            if not missing_fields:
                return sql
            
            # 增强SELECT子句
            if "SELECT" in sql_upper:
                select_start = sql_upper.find("SELECT") + 6
                from_pos = sql_upper.find("FROM")
                
                if from_pos > select_start:
                    current_fields = sql[select_start:from_pos].strip()
                    
                    # 如果是SELECT *，替换为具体字段
                    if current_fields.strip() == "*":
                        # 根据表名确定字段前缀
                        table_alias = self._extract_table_alias(sql)
                        enhanced_fields = []
                        
                        for field in auto_fields:
                            if table_alias:
                                enhanced_fields.append(f"{table_alias}.{field}")
                            else:
                                enhanced_fields.append(field)
                        
                        enhanced_sql = sql.replace("SELECT *", f"SELECT {', '.join(enhanced_fields)}")
                    else:
                        # 添加缺失字段
                        table_alias = self._extract_table_alias(sql)
                        additional_fields = []
                        
                        for field in missing_fields:
                            if table_alias:
                                additional_fields.append(f"{table_alias}.{field}")
                            else:
                                additional_fields.append(field)
                        
                        if additional_fields:
                            enhanced_sql = sql.replace(
                                current_fields,
                                f"{current_fields}, {', '.join(additional_fields)}"
                            )
                        else:
                            enhanced_sql = sql
                    
                    # 应用LIMIT限制
                    if "LIMIT" not in sql_upper and "limit" in template:
                        enhanced_sql += f" LIMIT {template['limit']}"
                    
                    return enhanced_sql
            
            return sql
            
        except Exception as e:
            logger.error(f"模板增强失败: {e}")
            return sql
    
    def _extract_table_alias(self, sql: str) -> Optional[str]:
        """提取表别名"""
        try:
            # 简单的别名提取逻辑
            sql_upper = sql.upper()
            from_pos = sql_upper.find("FROM")
            
            if from_pos == -1:
                return None
            
            # 查找FROM后的内容
            from_clause = sql[from_pos + 4:].strip()
            
            # 提取表名和别名
            parts = from_clause.split()
            if len(parts) >= 2:
                # 检查是否有AS关键字
                if len(parts) >= 3 and parts[1].upper() == "AS":
                    return parts[2].split()[0]  # 取别名的第一部分
                else:
                    # 直接跟在表名后的可能是别名
                    potential_alias = parts[1].split()[0]
                    if not potential_alias.upper() in ["WHERE", "GROUP", "ORDER", "HAVING", "LIMIT", "JOIN"]:
                        return potential_alias
            
            return None
            
        except Exception as e:
            logger.error(f"提取表别名失败: {e}")
            return None
    
    def suggest_additional_fields(self, query: str, current_fields: List[str]) -> List[str]:
        """
        根据查询内容建议额外的字段
        
        Args:
            query: 用户查询
            current_fields: 当前已有字段
            
        Returns:
            建议添加的字段列表
        """
        query_intent, _ = self.analyze_query_intent(query)
        
        # 获取增强配置
        enhancers = self.config.get("data_enhancers", {})
        suggestions = []
        
        # 根据查询意图获取建议字段
        intent_mapping = {
            "达人排行": "达人基本信息",
            "作品排行": "作品基本信息",
            "互动分析": "互动数据",
            "电商分析": "电商数据",
            "地区分析": "地区分析"
        }
        
        enhancer_type = intent_mapping.get(query_intent)
        if enhancer_type and enhancer_type in enhancers:
            enhancer_config = enhancers[enhancer_type]
            
            # 添加必需字段
            required_fields = enhancer_config.get("required_fields", [])
            for field in required_fields:
                if field not in current_fields:
                    suggestions.append(field)
            
            # 添加可选字段（根据查询内容判断相关性）
            optional_fields = enhancer_config.get("optional_fields", [])
            for field in optional_fields:
                if field not in current_fields and self._is_field_relevant(field, query):
                    suggestions.append(field)
        
        return suggestions
    
    def _is_field_relevant(self, field: str, query: str) -> bool:
        """判断字段是否与查询相关"""
        field_keywords = {
            "author_avatar": ["头像", "图片"],
            "desc": ["简介", "描述", "介绍"],
            "region": ["地区", "区域", "国家", "位置"],
            "is_verified": ["认证", "验证"],
            "commerce_user": ["电商", "商业"],
            "hashtags": ["标签", "话题", "tag"],
            "publish_time": ["时间", "发布", "日期"],
            "thumbnail_link": ["封面", "缩略图"]
        }
        
        query_lower = query.lower()
        keywords = field_keywords.get(field, [])
        
        return any(keyword in query_lower for keyword in keywords)
    
    def get_enhancement_summary(self, query: str) -> Dict[str, Any]:
        """
        获取增强建议摘要
        
        Args:
            query: 用户查询
            
        Returns:
            增强建议摘要
        """
        query_intent, matched_keywords = self.analyze_query_intent(query)
        
        return {
            "query_intent": query_intent,
            "matched_patterns": matched_keywords,
            "suggested_enhancement": self._get_enhancement_description(query_intent),
            "auto_fields": self._get_auto_fields(query_intent),
            "calculated_fields": self._get_calculated_fields(query_intent)
        }
    
    def _get_enhancement_description(self, query_intent: str) -> str:
        """获取增强描述"""
        descriptions = {
            "达人排行": "自动补充达人基本信息，包括用户名、昵称、粉丝数、获赞数等关键指标",
            "作品排行": "自动补充作品基本信息，包括标题、作者、播放量、互动数据等",
            "互动分析": "自动计算互动率、点赞率等衍生指标，提供完整的互动数据分析",
            "电商分析": "补充电商相关字段，包括电商功能状态、商品信息等",
            "地区分析": "补充地区和语言相关字段，支持地理位置分析",
            "时间分析": "补充时间相关字段，支持趋势和时间序列分析"
        }
        
        return descriptions.get(query_intent, "根据查询内容智能补充相关字段")
    
    def _get_auto_fields(self, query_intent: str) -> List[str]:
        """获取自动字段列表"""
        templates = self.config.get("query_templates", {})
        intent_mapping = {
            "达人排行": "达人排行",
            "作品排行": "作品排行",
            "互动分析": "互动分析"
        }
        
        template_key = intent_mapping.get(query_intent)
        if template_key and template_key in templates:
            return templates[template_key].get("auto_fields", [])
        
        return []
    
    def _get_calculated_fields(self, query_intent: str) -> List[str]:
        """获取计算字段列表"""
        enhancers = self.config.get("data_enhancers", {})
        intent_mapping = {
            "互动分析": "互动数据"
        }
        
        enhancer_key = intent_mapping.get(query_intent)
        if enhancer_key and enhancer_key in enhancers:
            return enhancers[enhancer_key].get("calculated_fields", [])
        
        return []
