"""
数据管理工具
管理缓存、训练数据和临时文件
"""

import os
import shutil
import glob
from pathlib import Path
from datetime import datetime


class DataManager:
    """数据管理器"""

    def __init__(self):
        self.data_dir = Path("data")
        self.cache_dir = self.data_dir / "cache"
        self.training_dir = self.data_dir / "training"
        self.logs_dir = self.data_dir / "logs"
        self.temp_dir = self.data_dir / "temp"
        self.exports_dir = self.data_dir / "exports"

        # 确保目录存在
        self.ensure_directories()
    
    def clean_cache(self):
        """清理缓存数据"""
        print("🧹 清理缓存数据...")
        
        cache_patterns = [
            self.cache_dir / "chromadb" / "*.sqlite3",
            self.cache_dir / "chromadb" / "*.lock",
            self.cache_dir / "python" / "__pycache__",
            self.temp_dir / "*"
        ]
        
        for pattern in cache_patterns:
            for item in glob.glob(str(pattern)):
                try:
                    if os.path.isfile(item):
                        os.remove(item)
                        print(f"  ✅ 删除文件: {item}")
                    elif os.path.isdir(item):
                        shutil.rmtree(item)
                        print(f"  ✅ 删除目录: {item}")
                except Exception as e:
                    print(f"  ❌ 删除失败 {item}: {e}")
    
    def backup_training_data(self, backup_name=None):
        """备份训练数据"""
        if not backup_name:
            from datetime import datetime
            backup_name = f"training_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        backup_dir = self.data_dir / "backups" / backup_name
        backup_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"💾 备份训练数据到: {backup_dir}")
        
        if self.training_dir.exists():
            shutil.copytree(self.training_dir, backup_dir / "training", dirs_exist_ok=True)
            print("  ✅ 训练数据备份完成")
        
        if (self.cache_dir / "chromadb").exists():
            shutil.copytree(self.cache_dir / "chromadb", backup_dir / "chromadb", dirs_exist_ok=True)
            print("  ✅ ChromaDB 数据备份完成")
    
    def get_storage_info(self):
        """获取存储信息"""
        print("📊 数据存储信息:")
        
        directories = [
            ("缓存数据", self.cache_dir),
            ("训练数据", self.training_dir),
            ("日志文件", self.logs_dir),
            ("临时文件", self.temp_dir),
            ("导出数据", self.exports_dir)
        ]
        
        for name, path in directories:
            if path.exists():
                size = sum(f.stat().st_size for f in path.rglob('*') if f.is_file())
                size_mb = size / (1024 * 1024)
                file_count = len(list(path.rglob('*')))
                print(f"  {name}: {size_mb:.2f} MB ({file_count} 个文件)")
            else:
                print(f"  {name}: 不存在")
    
    def ensure_directories(self):
        """确保所有必要目录存在"""
        directories = [
            self.cache_dir / "chromadb",
            self.cache_dir / "python",
            self.training_dir / "vanna",
            self.training_dir / "models",
            self.logs_dir,
            self.temp_dir,
            self.exports_dir
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)


def main():
    """主函数"""
    manager = DataManager()
    
    print("🗂️ 数据管理工具")
    print("=" * 30)
    
    # 确保目录存在
    manager.ensure_directories()
    
    # 显示存储信息
    manager.get_storage_info()
    
    print("\n💡 可用命令:")
    print("  manager.clean_cache()           # 清理缓存")
    print("  manager.backup_training_data()  # 备份训练数据")
    print("  manager.get_storage_info()      # 查看存储信息")


if __name__ == "__main__":
    main()
