"""
Vanna 路径配置更新
确保 Vanna 使用新的数据目录结构
"""

import os
from pathlib import Path


def get_chromadb_path():
    """获取 ChromaDB 数据库路径"""
    data_dir = Path("data")
    chromadb_path = data_dir / "cache" / "chromadb" / "chroma.sqlite3"
    
    # 确保目录存在
    chromadb_path.parent.mkdir(parents=True, exist_ok=True)
    
    return str(chromadb_path)


def get_training_config_path():
    """获取训练配置文件路径"""
    data_dir = Path("data")
    config_path = data_dir / "training" / "training_config.json"
    
    return str(config_path)


def setup_vanna_paths():
    """设置 Vanna 相关路径"""
    # 设置环境变量，供 Vanna 使用
    os.environ["VANNA_CHROMADB_PATH"] = get_chromadb_path()
    os.environ["VANNA_TRAINING_CONFIG"] = get_training_config_path()
    
    print(f"✅ ChromaDB 路径: {get_chromadb_path()}")
    print(f"✅ 训练配置路径: {get_training_config_path()}")


# 在导入时自动设置路径
setup_vanna_paths()
