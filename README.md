# TikTok 数据分析智能体项目

这是一个基于 CrewAI 和 Chainlit 的 TikTok 数据分析智能体项目，能够通过自然语言查询分析 TikTok 数据并生成专业报告。

## 项目特点

- 🤖 **多智能体协作**: 使用 CrewAI 框架，数据分析师和报告撰写人协同工作
- 🎯 **角色化模型**: 不同角色使用不同的 LLM 模型，优化性能和成本
- 🌐 **多模型支持**: 支持 OpenAI GPT 和阿里云通义千问系列模型
- 💬 **友好界面**: 基于 Chainlit 的聊天界面，支持自然语言交互
- 🗄️ **智能查询**: 集成 Vanna 库，自动将自然语言转换为 SQL 查询
- 📊 **专业报告**: 自动生成格式化的 Markdown 分析报告

## 项目结构

```
rps/
│
├── .env                  # 存储API密钥和数据库连接信息 (严禁上传到Git)
├── .env.example          # .env 的模板文件，说明需要哪些环境变量
├── app.py                # Chainlit 应用的主入口文件
├── requirements.txt      # 项目的所有Python依赖包
├── README.md             # 项目介绍、安装和运行指南
│
└── src/                  # 存放所有核心源代码的目录
    ├── __init__.py         # 将 src 目录标记为Python包
    ├── config.py           # 读取.env文件，并配置模型映射等全局设置
    ├── agents.py           # 定义所有的CrewAI Agents (数据分析师, 报告生成器等)
    ├── tasks.py            # 定义所有的CrewAI Tasks (数据查询任务, 报告撰写任务等)
    ├── crew.py             # 组装Agents和Tasks，定义并创建Crew
    │
    └── tools/              # 存放给Agent使用的自定义工具
        ├── __init__.py     # 将 tools 目录标记为Python子包
        └── database_tools.py # 封装Vanna库，提供与数据库交互的工具
```

## 安装和运行

### 1. 环境准备

```bash
# 创建虚拟环境
python -m venv .venv

# 激活虚拟环境 (Windows)
.venv\Scripts\activate

# 激活虚拟环境 (Linux/Mac)
source .venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 2. 环境配置

复制 `.env.example` 为 `.env` 并填入你的配置：

```bash
cp .env.example .env
```

编辑 `.env` 文件，添加必要的 API 密钥和数据库连接信息。

### 3. 训练 Vanna 模型

在首次使用前，需要训练 Vanna 模型来学习您的数据库结构：

```bash
# 生成训练配置模板
python train_vanna.py --sample

# 编辑 training_config.json 文件，添加您的数据库结构

# 开始训练
python train_vanna.py --config training_config.json

# 测试训练效果
python train_vanna.py --test "查询粉丝数最多的达人"
```

详细的训练指南请参考 [VANNA_TRAINING_GUIDE.md](VANNA_TRAINING_GUIDE.md)

### 4. 运行应用

```bash
# 使用启动脚本（推荐）
python run.py

# 或直接启动
chainlit run app.py -w
```

然后在浏览器中访问 `http://localhost:8000`

## 使用示例

在聊天界面中输入类似以下的查询：

- "帮我分析一下上周粉丝增长最快的达人是谁，并总结他的热门作品特点"
- "查询最近一个月播放量超过100万的视频，分析它们的共同特征"
- "分析美妆类达人的粉丝画像和内容偏好"

### 模型选择示例

- "通义模式 分析美妆类达人的内容策略" - 使用阿里云通义千问
- "openai模式 分析数据趋势" - 使用 OpenAI GPT
- "深度模式 通义模式 分析用户行为" - 深度分析 + 通义模型

## 技术栈

- **CrewAI**: 多智能体协作框架
- **Chainlit**: 聊天界面框架
- **Vanna**: 自然语言到SQL转换
- **LangChain**: LLM 集成和管理
- **MySQL**: 数据库存储

## 开发说明

项目采用模块化设计，每个组件职责明确：

- `config.py`: 统一管理配置和模型分配
- `agents.py`: 定义不同角色的智能体
- `tasks.py`: 定义具体的执行任务
- `crew.py`: 组装智能体团队
- `tools/`: 自定义工具集合
- `vanna_trainer.py`: Vanna 模型训练模块
- `train_vanna.py`: 训练脚本和命令行工具

## 📁 重要文件

- `training_config.json`: Vanna 训练配置文件
- `VANNA_TRAINING_GUIDE.md`: 详细的训练指南
- `TONGYI_SETUP_GUIDE.md`: 通义模型配置指南
- `QUICK_START.md`: 快速开始指南
- `test_components.py`: 组件测试脚本
- `run.py`: 应用启动脚本