"""
数据库工具模块 - 封装 Vanna 库，提供与数据库交互的工具
"""

from crewai.tools import BaseTool
from pydantic import Field
from typing import Optional, Type, Any
import pandas as pd
import logging
import mysql.connector
import os
import json
from ..config import DATABASE_CONFIG, VANNA_CONFIG, VANNA_DB_CONFIG
from ..vanna_paths import setup_vanna_paths

logger = logging.getLogger(__name__)

# 全局 Vanna 实例
_global_vn_instance = None
_global_vanna_available = False


class DatabaseQueryTool(BaseTool):
    """TikTok 数据库查询工具 - 增强版，支持智能数据格式化"""

    name: str = "TikTok 数据库查询工具"
    description: str = """
    用于将自然语言问题转换为SQL并查询TikTok数据库。
    输入应该是一个关于达人、作品或数据的具体问题。
    例如：'查询上周粉丝增长最快的达人' 或 '分析播放量超过100万的视频特征'

    增强功能：
    - 自动将数据库字段转换为中文说明
    - 根据查询意图智能补充相关字段
    - 提供结构化的数据返回格式
    - 自动计算衍生指标（如互动率）

    注意：使用此工具前，请确保已通过 train_vanna.py 脚本训练了 Vanna 模型。
    """

    def __init__(self, **data):
        super().__init__(**data)
        global _global_vn_instance, _global_vanna_available
        if _global_vn_instance is None:
            self._setup_vanna()

        # 初始化数据格式化器和增强器
        self._init_enhancers()

    def _init_enhancers(self):
        """初始化数据格式化器和增强器"""
        try:
            # 延迟导入避免循环依赖
            from .data_formatter import DataFormatter
            from .data_enhancer import DataEnhancer

            self._data_formatter = DataFormatter()
            self._data_enhancer = DataEnhancer()
            logger.info("数据格式化器和增强器初始化成功")
        except Exception as e:
            logger.error(f"数据格式化器初始化失败: {e}")
            # 设置为None，后续会有回退处理
            self._data_formatter = None
            self._data_enhancer = None

    @property
    def data_formatter(self):
        """获取数据格式化器"""
        return getattr(self, '_data_formatter', None)

    @property
    def data_enhancer(self):
        """获取数据增强器"""
        return getattr(self, '_data_enhancer', None)

    def _setup_vanna(self):
        """设置 Vanna 实例并连接数据库"""
        global _global_vn_instance, _global_vanna_available

        try:
            # 检查是否有 Vanna API key
            vanna_api_key = VANNA_CONFIG.get("api_key")
            vanna_model = VANNA_CONFIG.get("model")

            if vanna_api_key and vanna_api_key.strip():
                # 使用 Vanna Cloud
                logger.info("检测到 Vanna API Key，使用远程模式...")
                from vanna.remote import VannaDefault

                _global_vn_instance = VannaDefault(
                    model=vanna_model, api_key=vanna_api_key
                )
                logger.info("使用 Vanna Cloud 模式")
            else:
                # 使用本地模式
                logger.info("未检测到 Vanna API Key，使用本地模式...")
                from vanna.local import LocalContext_OpenAI

                openai_key = os.getenv("OPENAI_API_KEY")
                if not openai_key:
                    raise Exception("使用本地模式需要 OPENAI_API_KEY")

                # 准备 OpenAI 配置
                openai_config = {
                    "api_key": openai_key,
                    "temperature": 0.1,
                }

                # 检查是否有自定义 base_url
                base_url = os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1")
                if base_url != "https://api.openai.com/v1":
                    logger.info(f"使用自定义 OpenAI base_url: {base_url}")
                    openai_config["base_url"] = base_url

                _global_vn_instance = LocalContext_OpenAI(config=openai_config)
                logger.info(f"使用 Vanna 本地模式 (base_url: {base_url})")

            # 连接数据库（使用专用配置）
            _global_vn_instance.connect_to_mysql(**VANNA_DB_CONFIG)

            # 设置 Vanna 权限配置 - 安全方式
            try:
                _global_vn_instance.allow_llm_to_see_data = True
                logger.info("设置 allow_llm_to_see_data = True")
            except Exception as e:
                logger.warning(f"无法设置 allow_llm_to_see_data: {e}")

            # 如果有config属性，也尝试设置一下
            try:
                if hasattr(_global_vn_instance, 'config') and _global_vn_instance.config is not None:
                    if hasattr(_global_vn_instance.config, 'allow_llm_to_see_data'):
                        _global_vn_instance.config.allow_llm_to_see_data = True
                        logger.info("设置 config.allow_llm_to_see_data = True")
                    elif isinstance(_global_vn_instance.config, dict):
                        _global_vn_instance.config['allow_llm_to_see_data'] = True
                        logger.info("设置 config['allow_llm_to_see_data'] = True")
            except Exception as e:
                logger.warning(f"无法通过config设置权限: {e}")

            # 验证设置是否生效
            try:
                if hasattr(_global_vn_instance, 'allow_llm_to_see_data'):
                    logger.info(f"Vanna LLM 数据库内省权限: {_global_vn_instance.allow_llm_to_see_data}")
                else:
                    logger.warning("无法验证 Vanna LLM 数据库内省权限设置")
            except Exception as e:
                logger.warning(f"权限验证失败: {e}")

            logger.info("已启用 Vanna LLM 数据库内省功能")

            _global_vanna_available = True
            logger.info("Vanna 数据库连接成功")

        except Exception as e:
            logger.error(f"Vanna 设置失败: {e}")
            _global_vanna_available = False

    def _run(self, question: str) -> str:
        """执行查询 - 增强版，支持智能数据格式化"""
        global _global_vn_instance, _global_vanna_available

        if not _global_vanna_available:
            return "数据库连接不可用，请检查配置"

        try:
            # 确保 Vanna 权限设置正确
            if _global_vn_instance is not None:
                _global_vn_instance.allow_llm_to_see_data = True
                logger.debug("重新确认 Vanna LLM 数据库内省权限")

            # 检查增强器是否可用
            if self.data_enhancer is not None:
                # 分析查询意图
                query_intent, matched_keywords = self.data_enhancer.analyze_query_intent(question)
                logger.info(f"查询意图: {query_intent}, 匹配关键词: {matched_keywords}")
            else:
                query_intent = "通用查询"
                logger.warning("数据增强器不可用，使用默认查询意图")

            # 生成 SQL
            original_sql = _global_vn_instance.generate_sql(question)
            logger.info(f"原始 SQL: {original_sql}")

            # 根据查询意图增强 SQL（如果增强器可用）
            if self.data_enhancer is not None:
                enhanced_sql = self.data_enhancer.enhance_sql_query(original_sql, query_intent)
                if enhanced_sql != original_sql:
                    logger.info(f"增强后 SQL: {enhanced_sql}")
                    sql_to_execute = enhanced_sql
                else:
                    sql_to_execute = original_sql
            else:
                sql_to_execute = original_sql

            # 执行 SQL 查询
            result = _global_vn_instance.run_sql(sql_to_execute)

            # 处理结果
            if isinstance(result, pd.DataFrame):
                if result.empty:
                    return self._format_empty_result(question, sql_to_execute)

                # 使用增强的数据格式化器（如果可用）
                if self.data_formatter is not None:
                    formatted_result = self._format_enhanced_dataframe(
                        result, question, sql_to_execute, query_intent
                    )
                    return formatted_result
                else:
                    # 回退到基本格式化
                    logger.warning("数据格式化器不可用，使用基本格式化")
                    return self._format_dataframe_fallback(result, sql_to_execute)

            return f"## 查询结果\n\n**执行的 SQL:**\n```sql\n{sql_to_execute}\n```\n\n**结果:** {str(result)}"

        except Exception as e:
            logger.error(f"查询执行失败: {e}")
            return f"❌ **查询执行失败**\n\n**错误信息:** {str(e)}\n\n**建议:**\n- 检查查询语句是否正确\n- 确认数据库连接正常\n- 尝试重新表述问题"

    def _format_enhanced_dataframe(self, df: pd.DataFrame, question: str,
                                  sql: str, query_intent: str) -> str:
        """增强版DataFrame格式化"""
        try:
            # 检查数据格式化器是否可用
            if self.data_formatter is None:
                logger.warning("数据格式化器不可用，使用回退格式化")
                return self._format_dataframe_fallback(df, sql)

            # 确定表名（用于字段映射）
            table_name = self._extract_table_name(sql)

            # 确定增强类型
            enhance_type = self._map_intent_to_enhance_type(query_intent)

            # 使用数据格式化器处理数据
            formatted_data = self.data_formatter.format_dataframe(
                df, table_name=table_name, enhance_type=enhance_type
            )

            # 构建返回结果
            if formatted_data["status"] == "success":
                result_parts = []

                # 标题和基本信息
                result_parts.append("## 📊 查询结果")
                result_parts.append(f"\n**查询问题:** {question}")
                result_parts.append(f"**查询意图:** {query_intent}")
                result_parts.append(f"**数据总数:** {formatted_data['total']} 条")

                # SQL信息
                result_parts.append(f"\n**执行的 SQL:**")
                result_parts.append(f"```sql\n{sql}\n```")

                # 数据表格
                if formatted_data["data"]:
                    result_parts.append("\n**查询结果:**")

                    # 转换为DataFrame并格式化为表格
                    result_df = pd.DataFrame(formatted_data["data"])

                    # 限制显示行数
                    max_rows = 15
                    if len(result_df) > max_rows:
                        display_df = result_df.head(max_rows)
                        truncated_note = f"\n\n*注：仅显示前 {max_rows} 行，总共 {len(result_df)} 行数据*"
                    else:
                        display_df = result_df
                        truncated_note = ""

                    # 生成表格
                    markdown_table = display_df.to_markdown(index=False, tablefmt="pipe")
                    result_parts.append(markdown_table)
                    result_parts.append(truncated_note)

                # 汇总统计
                if formatted_data["summary"]:
                    result_parts.append("\n## 📈 数据汇总")
                    for key, value in formatted_data["summary"].items():
                        if isinstance(value, (int, float)):
                            if isinstance(value, float):
                                result_parts.append(f"- **{key}**: {value:,.2f}")
                            else:
                                result_parts.append(f"- **{key}**: {value:,}")
                        else:
                            result_parts.append(f"- **{key}**: {value}")

                # 字段说明
                if formatted_data.get("fields_description"):
                    result_parts.append("\n## 📝 字段说明")
                    for field, desc in formatted_data["fields_description"].items():
                        result_parts.append(f"- **{field}**: {desc}")

                return "\n".join(result_parts)

            else:
                return f"❌ **数据格式化失败**\n\n{formatted_data['message']}"

        except Exception as e:
            logger.error(f"增强格式化失败: {e}")
            # 回退到原始格式化方法
            return self._format_dataframe_fallback(df, sql)

    def _format_dataframe_fallback(self, df: pd.DataFrame, sql: str) -> str:
        """回退的DataFrame格式化方法"""
        try:
            # 限制显示的行数
            max_rows = 20
            if len(df) > max_rows:
                df_display = df.head(max_rows)
                truncated_note = (
                    f"\n\n*注：仅显示前 {max_rows} 行，总共 {len(df)} 行数据*"
                )
            else:
                df_display = df
                truncated_note = f"\n\n*总共 {len(df)} 行数据*"

            # 转换为 Markdown 表格
            markdown_table = df_display.to_markdown(index=False, tablefmt="pipe")

            return f"## 查询结果\n\n**执行的 SQL:**\n```sql\n{sql}\n```\n\n**查询结果:**\n{markdown_table}{truncated_note}"

        except Exception as e:
            logger.error(f"回退格式化失败: {e}")
            # 最后的回退方案
            return f"## 查询结果\n\n**执行的 SQL:**\n```sql\n{sql}\n```\n\n**查询结果:**\n{df.to_string(index=False)}"

    def _format_empty_result(self, question: str, sql: str) -> str:
        """格式化空结果"""
        return f"""## 📊 查询结果

**查询问题:** {question}

**执行的 SQL:**
```sql
{sql}
```

**结果:** 查询未返回任何数据

**可能原因:**
- 查询条件过于严格，没有符合条件的数据
- 数据库中暂无相关数据
- 查询的时间范围内没有数据

**建议:**
- 尝试放宽查询条件
- 检查数据是否存在
- 修改时间范围或其他筛选条件"""

    def _extract_table_name(self, sql: str) -> str:
        """从SQL中提取主要表名"""
        try:
            sql_upper = sql.upper()

            # 查找FROM子句
            from_pos = sql_upper.find("FROM")
            if from_pos == -1:
                return None

            # 提取FROM后的内容
            from_clause = sql[from_pos + 4:].strip()

            # 提取表名（去掉别名）
            parts = from_clause.split()
            if parts:
                table_name = parts[0].strip('`').strip()
                # 如果包含数据库名，只取表名部分
                if '.' in table_name:
                    table_name = table_name.split('.')[-1]
                return table_name

            return None

        except Exception as e:
            logger.error(f"提取表名失败: {e}")
            return None

    def _map_intent_to_enhance_type(self, query_intent: str) -> str:
        """将查询意图映射到增强类型"""
        intent_mapping = {
            "达人排行": "达人基本信息",
            "作品排行": "作品基本信息",
            "互动分析": "互动数据",
            "电商分析": "电商数据",
            "地区分析": "地区分析"
        }

        return intent_mapping.get(query_intent, None)


class DatabaseConnectionTool(BaseTool):
    """数据库连接测试工具"""

    name: str = "数据库连接测试工具"
    description: str = "测试与 TikTok 数据库的连接状态"

    def _run(self, input_text: str = "") -> str:
        """测试数据库连接"""
        connection = None
        cursor = None
        try:
            # 使用优化的连接配置
            connection = mysql.connector.connect(**DATABASE_CONFIG)
            if connection.is_connected():
                cursor = connection.cursor()
                cursor.execute("SELECT VERSION()")
                version = cursor.fetchone()

                # 测试连接超时设置
                cursor.execute("SELECT @@wait_timeout, @@interactive_timeout")
                timeouts = cursor.fetchone()

                return f"""数据库连接成功！
MySQL 版本: {version[0]}
连接超时: {DATABASE_CONFIG.get('connection_timeout', 'N/A')}秒
服务器超时: wait_timeout={timeouts[0]}s, interactive_timeout={timeouts[1]}s
连接池: {DATABASE_CONFIG.get('pool_name', 'N/A')} (大小: {DATABASE_CONFIG.get('pool_size', 'N/A')})"""
            else:
                return "数据库连接失败"
        except Exception as e:
            return f"数据库连接错误: {str(e)}"
        finally:
            # 确保资源释放
            if cursor:
                cursor.close()
            if connection and connection.is_connected():
                connection.close()
