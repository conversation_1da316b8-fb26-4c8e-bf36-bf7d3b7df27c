"""
彻底修复嵌入维度不匹配问题
解决 ChromaDB 中混合了不同维度的嵌入数据
"""

import os
import shutil
import subprocess
import sys
from pathlib import Path


def stop_all_processes():
    """停止所有可能占用 ChromaDB 的进程"""
    print("🛑 停止相关进程...")
    
    try:
        # 在 Windows 上查找并终止可能的 Python 进程
        result = subprocess.run([
            "tasklist", "/FI", "IMAGENAME eq python.exe", "/FO", "CSV"
        ], capture_output=True, text=True)
        
        if "python.exe" in result.stdout:
            print("  ⚠️ 发现运行中的 Python 进程")
            print("  💡 请手动关闭所有 Chainlit 和 Python 应用")
            input("  按回车键继续...")
        
    except Exception as e:
        print(f"  ⚠️ 进程检查失败: {e}")


def nuclear_chromadb_cleanup():
    """核弹级 ChromaDB 清理"""
    print("💥 执行核弹级 ChromaDB 清理...")
    
    # 所有可能的 ChromaDB 位置
    cleanup_paths = [
        Path("data/cache/chromadb"),
        Path("data/cache"),
        Path(".chromadb"),
        Path("chromadb"),
        Path("chroma_db"),
        Path("./chroma.sqlite3"),
        Path("chroma.sqlite3")
    ]
    
    # 添加用户目录下的可能位置
    home_paths = [
        Path.home() / ".chromadb",
        Path.home() / "chromadb",
        Path.home() / ".cache" / "chromadb"
    ]
    cleanup_paths.extend(home_paths)
    
    # 添加临时目录
    import tempfile
    temp_paths = [
        Path(tempfile.gettempdir()) / "chromadb",
        Path(tempfile.gettempdir()) / "chroma"
    ]
    cleanup_paths.extend(temp_paths)
    
    cleaned_count = 0
    for path in cleanup_paths:
        if path.exists():
            try:
                if path.is_dir():
                    shutil.rmtree(path, ignore_errors=True)
                    print(f"  ✅ 删除目录: {path}")
                else:
                    path.unlink(missing_ok=True)
                    print(f"  ✅ 删除文件: {path}")
                cleaned_count += 1
            except Exception as e:
                print(f"  ⚠️ 删除失败 {path}: {e}")
    
    if cleaned_count == 0:
        print("  ✅ 没有找到需要清理的 ChromaDB 文件")
    else:
        print(f"  ✅ 清理了 {cleaned_count} 个位置")
    
    # 重新创建必要的目录
    data_dirs = [
        "data",
        "data/cache", 
        "data/cache/chromadb",
        "data/training",
        "data/logs",
        "data/temp"
    ]
    
    for dir_path in data_dirs:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
    
    print("  ✅ 重建目录结构完成")


def create_consistent_vanna_config():
    """创建一致的 Vanna 配置"""
    print("⚙️ 创建一致的 Vanna 配置...")
    
    config_content = '''"""
一致的 Vanna 配置
确保使用统一的嵌入模型
"""

import os
from pathlib import Path


def create_consistent_vanna():
    """创建使用一致嵌入的 Vanna 实例"""
    from vanna.openai import OpenAI_Chat
    from vanna.chromadb import ChromaDB_VectorStore
    
    class ConsistentVanna(ChromaDB_VectorStore, OpenAI_Chat):
        def __init__(self, config=None):
            # 强制使用新的 ChromaDB 路径
            chroma_config = {
                "path": str(Path("data/cache/chromadb").absolute()),
                "collection_name": "vanna_embeddings_v2"  # 使用新的集合名
            }
            ChromaDB_VectorStore.__init__(self, config=chroma_config)
            OpenAI_Chat.__init__(self, config=config)
    
    # 检查 OpenAI API Key
    openai_api_key = os.getenv("OPENAI_API_KEY")
    if not openai_api_key:
        raise ValueError("缺少 OPENAI_API_KEY 环境变量")
    
    config = {
        "api_key": openai_api_key,
        "model": "gpt-3.5-turbo",
        "base_url": os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1")
    }
    
    return ConsistentVanna(config=config)


def setup_database_connection(vn):
    """设置数据库连接"""
    from src.config import VANNA_DB_CONFIG
    
    vn.connect_to_mysql(**VANNA_DB_CONFIG)
    vn.allow_llm_to_see_data = True
    
    return vn
'''
    
    with open("src/consistent_vanna.py", "w", encoding="utf-8") as f:
        f.write(config_content)
    
    print("  ✅ 创建 src/consistent_vanna.py")


def update_database_tools():
    """更新数据库工具使用一致的 Vanna"""
    print("🔧 更新数据库工具...")
    
    tools_file = Path("src/tools/database_tools.py")
    if not tools_file.exists():
        print("  ❌ database_tools.py 不存在")
        return False
    
    # 读取文件内容
    with open(tools_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否需要更新
    if "from ..consistent_vanna import create_consistent_vanna" in content:
        print("  ✅ database_tools.py 已经更新")
        return True
    
    # 添加导入
    import_line = "from ..config import DATABASE_CONFIG, VANNA_CONFIG, VANNA_DB_CONFIG"
    new_import = import_line + "\nfrom ..consistent_vanna import create_consistent_vanna, setup_database_connection"
    
    content = content.replace(import_line, new_import)
    
    # 更新 _setup_vanna 方法
    old_setup = """        if vanna_api_key and vanna_model:
            print("📡 使用 Vanna 云端模式")
            from vanna.remote import VannaDefault
            _global_vn_instance = VannaDefault(model=vanna_model, api_key=vanna_api_key)
        else:
            print("💻 使用 Vanna 本地模式")
            # 检查本地模式配置
            openai_api_key = os.getenv("OPENAI_API_KEY")
            base_url = os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1")
            
            if not openai_api_key:
                logger.error("缺少 OPENAI_API_KEY 环境变量")
                _global_vanna_available = False
                return
            
            from vanna.openai import OpenAI_Chat
            from vanna.chromadb import ChromaDB_VectorStore
            
            class LocalContext_OpenAI(ChromaDB_VectorStore, OpenAI_Chat):
                def __init__(self, config=None):
                    ChromaDB_VectorStore.__init__(self, config=config)
                    OpenAI_Chat.__init__(self, config=config)
            
            openai_config = {
                "api_key": openai_api_key,
                "model": "gpt-3.5-turbo",
                "base_url": base_url
            }
            
            _global_vn_instance = LocalContext_OpenAI(config=openai_config)"""
    
    new_setup = """        # 使用一致的 Vanna 配置
        print("🔧 使用一致的 Vanna 配置")
        _global_vn_instance = create_consistent_vanna()"""
    
    content = content.replace(old_setup, new_setup)
    
    # 更新连接部分
    old_connect = """            # 连接数据库（使用专用配置）
            _global_vn_instance.connect_to_mysql(**VANNA_DB_CONFIG)"""
    
    new_connect = """            # 连接数据库（使用专用配置）
            _global_vn_instance = setup_database_connection(_global_vn_instance)"""
    
    content = content.replace(old_connect, new_connect)
    
    # 写回文件
    with open(tools_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("  ✅ database_tools.py 更新完成")
    return True


def run_minimal_training():
    """运行最小化训练"""
    print("🎓 运行最小化训练...")
    
    try:
        # 创建最小训练脚本
        training_script = '''
import sys
import os
from pathlib import Path

# 设置路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from src.consistent_vanna import create_consistent_vanna, setup_database_connection

def main():
    print("🎓 开始最小化训练...")
    
    try:
        # 创建实例
        vn = create_consistent_vanna()
        vn = setup_database_connection(vn)
        
        # 基本表结构
        ddl = """CREATE TABLE at_tiktok_author_pool (
            unique_id varchar(128) COMMENT '用户名',
            author_name varchar(255) COMMENT '达人昵称', 
            follower_count int COMMENT '粉丝数',
            is_del tinyint COMMENT '是否删除'
        );"""
        
        vn.train(ddl=ddl)
        print("  ✅ 表结构训练完成")
        
        # 基本查询
        vn.train(
            question="统计达人总数",
            sql="SELECT COUNT(*) as total FROM at_tiktok_author_pool WHERE is_del = 0;"
        )
        print("  ✅ 基本查询训练完成")
        
        # 测试
        sql = vn.generate_sql("统计达人总数")
        print(f"  生成的 SQL: {sql}")
        
        if "SELECT" in sql.upper():
            result = vn.run_sql(sql)
            print(f"  ✅ 测试成功: {result}")
            return True
        else:
            print(f"  ❌ SQL 生成失败: {sql}")
            return False
            
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
'''
        
        with open("data/training/minimal_train.py", "w", encoding="utf-8") as f:
            f.write(training_script)
        
        # 运行训练
        result = subprocess.run([
            sys.executable, "data/training/minimal_train.py"
        ], capture_output=True, text=True, cwd=os.getcwd())
        
        print("训练输出:")
        print(result.stdout)
        
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ 训练执行失败: {e}")
        return False


def test_final_functionality():
    """测试最终功能"""
    print("🧪 测试最终功能...")
    
    try:
        from src.tools.database_tools import DatabaseQueryTool
        
        tool = DatabaseQueryTool()
        result = tool._run("统计达人总数")
        
        if "Embedding dimension" in result:
            print("❌ 嵌入维度问题仍然存在")
            return False
        elif "查询执行失败" in result:
            print("❌ 查询执行失败")
            print(f"错误: {result}")
            return False
        else:
            print("✅ 功能测试成功")
            print(f"结果预览: {result[:200]}...")
            return True
            
    except Exception as e:
        print(f"❌ 功能测试异常: {e}")
        return False


def main():
    """主函数"""
    print("🔧 彻底修复嵌入维度不匹配问题")
    print("=" * 60)
    
    if not os.path.exists("src/config.py"):
        print("❌ 请从项目根目录运行")
        return
    
    print("⚠️ 此操作将:")
    print("  1. 完全删除所有 ChromaDB 数据")
    print("  2. 更新数据库工具配置")
    print("  3. 重新训练基本功能")
    
    confirm = input("\\n确定要继续吗? [y/N]: ").strip().lower()
    if confirm not in ['y', 'yes', '是']:
        print("❌ 操作取消")
        return
    
    # 步骤1: 停止进程
    stop_all_processes()
    
    # 步骤2: 核弹级清理
    nuclear_chromadb_cleanup()
    
    # 步骤3: 创建一致配置
    create_consistent_vanna_config()
    
    # 步骤4: 更新工具
    if not update_database_tools():
        print("❌ 工具更新失败")
        return
    
    # 步骤5: 最小化训练
    if not run_minimal_training():
        print("❌ 训练失败")
        return
    
    # 步骤6: 最终测试
    if test_final_functionality():
        print("\\n🎉 嵌入维度问题彻底修复成功！")
        print("\\n💡 现在可以正常使用:")
        print("  chainlit run app.py")
    else:
        print("\\n❌ 修复可能不完整，请检查错误信息")


if __name__ == "__main__":
    main()
