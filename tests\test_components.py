"""
组件测试脚本
用于测试各个模块是否正常工作
"""

import os
import sys
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()


def test_config():
    """测试配置模块"""
    print("🧪 测试配置模块...")
    try:
        from src.config import (
            get_llm_by_role,
            validate_config,
            DATABASE_CONFIG,
            get_available_models,
        )

        # 测试可用模型
        available_models = get_available_models()
        print(f"  🤖 可用模型: {len(available_models)} 个")
        for model in available_models:
            print(f"    - {model['description']}")

        # 测试模型获取
        has_openai = bool(os.getenv("OPENAI_API_KEY"))
        has_tongyi = bool(os.getenv("DASHSCOPE_API_KEY"))

        if has_openai or has_tongyi:
            try:
                analyst_llm = get_llm_by_role("data_analyst", "auto")
                writer_llm = get_llm_by_role("report_writer", "auto")
                print("  ✅ LLM 模型配置正常")
            except Exception as e:
                print(f"  ⚠️  LLM 模型配置警告: {e}")
        else:
            print("  ⚠️  未配置任何模型 API 密钥")

        # 测试数据库配置
        print(f"  📊 数据库配置: {DATABASE_CONFIG['host']}:{DATABASE_CONFIG['port']}")
        print("  ✅ 配置模块测试通过")
        return True

    except Exception as e:
        print(f"  ❌ 配置模块测试失败: {e}")
        return False


def test_database_tools():
    """测试数据库工具"""
    print("🧪 测试数据库工具...")
    try:
        from src.tools.database_tools import DatabaseConnectionTool

        # 创建连接测试工具
        connection_tool = DatabaseConnectionTool()

        # 测试连接（这可能会失败，因为可能没有实际的数据库）
        result = connection_tool._run("")
        print(f"  📊 数据库连接测试结果: {result}")
        print("  ✅ 数据库工具模块加载成功")
        return True

    except Exception as e:
        print(f"  ❌ 数据库工具测试失败: {e}")
        return False


def test_agents():
    """测试智能体"""
    print("🧪 测试智能体模块...")
    try:
        from src.agents import create_data_analyst_agent, create_report_writer_agent

        if (
            os.getenv("OPENAI_API_KEY")
            and os.getenv("OPENAI_API_KEY") != "your-openai-api-key-here"
        ):
            # 创建智能体
            analyst = create_data_analyst_agent()
            writer = create_report_writer_agent()

            print(f"  🤖 数据分析师: {analyst.role}")
            print(f"  ✍️  报告撰写人: {writer.role}")
            print("  ✅ 智能体创建成功")
        else:
            print("  ⚠️  OPENAI_API_KEY 未配置，跳过智能体创建测试")

        return True

    except Exception as e:
        print(f"  ❌ 智能体测试失败: {e}")
        return False


def test_tasks():
    """测试任务模块"""
    print("🧪 测试任务模块...")
    try:
        from src.tasks import create_data_query_task, create_report_writing_task
        from src.agents import create_data_analyst_agent, create_report_writer_agent

        if (
            os.getenv("OPENAI_API_KEY")
            and os.getenv("OPENAI_API_KEY") != "your-openai-api-key-here"
        ):
            # 创建智能体
            analyst = create_data_analyst_agent()
            writer = create_report_writer_agent()

            # 创建任务
            data_task = create_data_query_task("测试查询", analyst)
            report_task = create_report_writing_task("测试报告", writer)

            print(f"  📊 数据查询任务: {data_task.description[:50]}...")
            print(f"  📝 报告撰写任务: {report_task.description[:50]}...")
            print("  ✅ 任务创建成功")
        else:
            print("  ⚠️  OPENAI_API_KEY 未配置，跳过任务创建测试")

        return True

    except Exception as e:
        print(f"  ❌ 任务测试失败: {e}")
        return False


def test_crew():
    """测试团队组装"""
    print("🧪 测试团队组装...")
    try:
        from src.crew import create_simple_crew

        if (
            os.getenv("OPENAI_API_KEY")
            and os.getenv("OPENAI_API_KEY") != "your-openai-api-key-here"
        ):
            # 创建团队
            crew = create_simple_crew("测试分析主题")

            print(f"  👥 团队智能体数量: {len(crew.agents)}")
            print(f"  📋 团队任务数量: {len(crew.tasks)}")
            print("  ✅ 团队组装成功")
        else:
            print("  ⚠️  OPENAI_API_KEY 未配置，跳过团队创建测试")

        return True

    except Exception as e:
        print(f"  ❌ 团队测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🧪 TikTok 数据分析智能体 - 组件测试")
    print("=" * 60)

    tests = [
        ("配置模块", test_config),
        ("数据库工具", test_database_tools),
        ("智能体模块", test_agents),
        ("任务模块", test_tasks),
        ("团队组装", test_crew),
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 40)
        if test_func():
            passed += 1

    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")

    if passed == total:
        print("🎉 所有测试通过！项目组件正常工作。")
        print("💡 您可以运行 'python run.py' 启动应用")
    else:
        print("⚠️  部分测试失败，请检查配置和依赖")
        print("💡 请确保:")
        print("   1. 已安装所有依赖: pip install -r requirements.txt")
        print("   2. 已正确配置 .env 文件")
        print("   3. API 密钥有效且有足够额度")


if __name__ == "__main__":
    main()
