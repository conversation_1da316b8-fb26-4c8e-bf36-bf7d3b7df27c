"""
数据格式化工具模块
将数据库原始字段转换为中文友好的字段说明，并提供结构化的数据返回格式
"""

import pandas as pd
from typing import Dict, List, Any, Optional
import json
import logging
from datetime import datetime

logger = logging.getLogger(__name__)


class DataFormatter:
    """数据格式化器 - 将数据库原始数据转换为用户友好的格式"""
    
    def __init__(self):
        """初始化数据格式化器"""
        self.field_mappings = self._load_field_mappings()
        self.data_enhancers = self._load_data_enhancers()
    
    def _load_field_mappings(self) -> Dict[str, Dict[str, str]]:
        """加载字段映射配置"""
        try:
            # 尝试从配置文件加载
            import os
            config_path = os.path.join(os.path.dirname(__file__), '..', 'config', 'field_mappings.json')
            if os.path.exists(config_path):
                import json
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    return config.get('field_mappings', {})
        except Exception as e:
            logger.error(f"加载字段映射配置失败: {e}")

        # 回退到硬编码配置
        return {
            "at_tiktok_author_pool": {
                "id": "ID",
                "author_id": "达人ID",
                "unique_id": "用户名",
                "author_name": "达人昵称",
                "author_avatar": "头像链接",
                "sec_uid": "加密ID",
                "author_url": "主页地址",
                "desc": "个人简介",
                "register_time": "注册时间",
                "is_verified": "认证状态",
                "region": "注册地区",
                "language": "界面语言",
                "private_account": "私密账号",
                "commerce_user": "电商功能",
                "tt_seller": "小店卖家",
                "commerce_category": "电商类目",
                "follower_count": "粉丝数",
                "following_count": "关注数",
                "heart_count": "总获赞数",
                "video_count": "发布视频数",
                "friend_count": "好友数",
                "update_status": "更新状态",
                "creator": "创建者",
                "create_time": "创建时间",
                "updater": "更新人",
                "update_time": "更新时间",
                "is_del": "删除状态",
                "status": "监控状态"
            },
            "at_tiktok_author_work_record": {
                "id": "ID",
                "work_id": "作品ID",
                "work_uuid": "作品UUID",
                "author_id": "达人ID",
                "unique_id": "用户名",
                "sec_uid": "加密ID",
                "url": "作品链接",
                "category_type": "分类类型",
                "thumbnail_link": "封面图",
                "is_ad": "广告标识",
                "title": "作品标题",
                "content": "作品描述",
                "hashtags": "话题标签",
                "images": "图片链接",
                "publish_time": "发布时间",
                "text_language": "文本语言",
                "location_ip": "发布位置",
                "play_count": "播放量",
                "like_count": "点赞数",
                "comment_count": "评论数",
                "share_count": "分享数",
                "collect_count": "收藏数",
                "video_id": "视频ID",
                "music_id": "音乐ID",
                "product_ids": "商品ID",
                "creator": "创建者",
                "create_time": "创建时间",
                "updater": "更新人",
                "update_time": "更新时间"
            }
        }
    
    def _load_data_enhancers(self) -> Dict[str, Dict[str, Any]]:
        """加载数据增强配置"""
        try:
            # 尝试从配置文件加载
            import os
            config_path = os.path.join(os.path.dirname(__file__), '..', 'config', 'field_mappings.json')
            if os.path.exists(config_path):
                import json
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    return config.get('data_enhancers', {})
        except Exception as e:
            logger.error(f"加载数据增强配置失败: {e}")

        # 回退到硬编码配置
        return {
            "达人基本信息": {
                "required_fields": ["unique_id", "author_name", "follower_count", "heart_count", "video_count"],
                "optional_fields": ["author_avatar", "desc", "region", "is_verified", "commerce_user"],
                "description": "达人的基本信息，包括用户名、昵称、粉丝数等关键指标"
            },
            "作品基本信息": {
                "required_fields": ["title", "play_count", "like_count", "comment_count", "share_count"],
                "optional_fields": ["thumbnail_link", "hashtags", "publish_time", "is_ad"],
                "description": "作品的基本信息，包括标题、播放量、互动数据等"
            },
            "互动数据": {
                "required_fields": ["play_count", "like_count", "comment_count", "share_count", "collect_count"],
                "calculated_fields": ["engagement_rate", "like_rate", "comment_rate"],
                "description": "作品的互动数据和计算指标"
            }
        }
    
    def format_dataframe(self, df: pd.DataFrame, table_name: str = None, 
                        enhance_type: str = None) -> Dict[str, Any]:
        """
        格式化DataFrame为用户友好的结构化数据
        
        Args:
            df: 原始DataFrame
            table_name: 表名，用于字段映射
            enhance_type: 数据增强类型
            
        Returns:
            格式化后的结构化数据
        """
        if df.empty:
            return {
                "status": "success",
                "message": "查询成功，但没有找到符合条件的数据",
                "data": [],
                "total": 0,
                "summary": {}
            }
        
        try:
            # 1. 字段名称转换
            formatted_df = self._translate_column_names(df, table_name)
            
            # 2. 数据值格式化
            formatted_df = self._format_data_values(formatted_df)
            
            # 3. 数据增强
            if enhance_type:
                formatted_df = self._enhance_data(formatted_df, enhance_type)
            
            # 4. 计算汇总统计
            summary = self._calculate_summary(formatted_df)
            
            # 5. 转换为结构化格式
            data_list = formatted_df.to_dict('records')
            
            return {
                "status": "success",
                "message": f"查询成功，共找到 {len(data_list)} 条数据",
                "data": data_list,
                "total": len(data_list),
                "summary": summary,
                "fields_description": self._get_fields_description(formatted_df.columns, table_name)
            }
            
        except Exception as e:
            logger.error(f"数据格式化失败: {e}")
            return {
                "status": "error",
                "message": f"数据格式化失败: {str(e)}",
                "data": [],
                "total": 0,
                "summary": {}
            }
    
    def _translate_column_names(self, df: pd.DataFrame, table_name: str = None) -> pd.DataFrame:
        """翻译列名为中文"""
        if not table_name or table_name not in self.field_mappings:
            return df
        
        mapping = self.field_mappings[table_name]
        new_columns = {}
        
        for col in df.columns:
            if col in mapping:
                new_columns[col] = mapping[col]
            else:
                # 如果没有映射，保持原名但添加提示
                new_columns[col] = col
        
        return df.rename(columns=new_columns)
    
    def _format_data_values(self, df: pd.DataFrame) -> pd.DataFrame:
        """格式化数据值"""
        formatted_df = df.copy()
        
        for col in formatted_df.columns:
            # 格式化时间戳
            if '时间' in col and formatted_df[col].dtype in ['int64', 'float64']:
                formatted_df[col] = formatted_df[col].apply(self._format_timestamp)
            
            # 格式化布尔值
            elif formatted_df[col].dtype == 'bool' or col in ['认证状态', '私密账号', '电商功能', '小店卖家', '广告标识']:
                formatted_df[col] = formatted_df[col].apply(self._format_boolean)
            
            # 格式化数字
            elif formatted_df[col].dtype in ['int64', 'float64'] and col in ['粉丝数', '播放量', '点赞数', '评论数', '分享数']:
                formatted_df[col] = formatted_df[col].apply(self._format_number)
        
        return formatted_df
    
    def _format_timestamp(self, value) -> str:
        """格式化时间戳"""
        if pd.isna(value) or value == 0:
            return "未知"
        
        try:
            if isinstance(value, (int, float)):
                dt = datetime.fromtimestamp(value)
            else:
                dt = pd.to_datetime(value)
            return dt.strftime("%Y-%m-%d %H:%M:%S")
        except:
            return str(value)
    
    def _format_boolean(self, value) -> str:
        """格式化布尔值"""
        if pd.isna(value):
            return "未知"
        
        if value in [1, True, '1', 'true', 'True']:
            return "是"
        elif value in [0, False, '0', 'false', 'False']:
            return "否"
        else:
            return str(value)
    
    def _format_number(self, value) -> str:
        """格式化数字（添加千分位分隔符）"""
        if pd.isna(value):
            return "0"
        
        try:
            num = int(float(value))
            if num >= 10000:
                return f"{num:,}"
            else:
                return str(num)
        except:
            return str(value)
    
    def _enhance_data(self, df: pd.DataFrame, enhance_type: str) -> pd.DataFrame:
        """数据增强 - 添加计算字段和相关信息"""
        enhanced_df = df.copy()
        
        if enhance_type == "互动数据":
            # 计算互动率
            if all(col in enhanced_df.columns for col in ['播放量', '点赞数', '评论数', '分享数']):
                enhanced_df['互动率(%)'] = enhanced_df.apply(
                    lambda row: self._calculate_engagement_rate(
                        row['播放量'], row['点赞数'], row['评论数'], row['分享数']
                    ), axis=1
                )
            
            # 计算点赞率
            if all(col in enhanced_df.columns for col in ['播放量', '点赞数']):
                enhanced_df['点赞率(%)'] = enhanced_df.apply(
                    lambda row: self._calculate_rate(row['点赞数'], row['播放量']), axis=1
                )
        
        return enhanced_df
    
    def _calculate_engagement_rate(self, play_count, like_count, comment_count, share_count) -> str:
        """计算互动率"""
        try:
            play = float(str(play_count).replace(',', '')) if play_count else 0
            like = float(str(like_count).replace(',', '')) if like_count else 0
            comment = float(str(comment_count).replace(',', '')) if comment_count else 0
            share = float(str(share_count).replace(',', '')) if share_count else 0
            
            if play > 0:
                rate = (like + comment + share) / play * 100
                return f"{rate:.2f}%"
            else:
                return "0.00%"
        except:
            return "0.00%"
    
    def _calculate_rate(self, numerator, denominator) -> str:
        """计算比率"""
        try:
            num = float(str(numerator).replace(',', '')) if numerator else 0
            den = float(str(denominator).replace(',', '')) if denominator else 0
            
            if den > 0:
                rate = num / den * 100
                return f"{rate:.2f}%"
            else:
                return "0.00%"
        except:
            return "0.00%"
    
    def _calculate_summary(self, df: pd.DataFrame) -> Dict[str, Any]:
        """计算汇总统计"""
        summary = {}
        
        # 数值列的统计
        numeric_columns = df.select_dtypes(include=['int64', 'float64']).columns
        for col in numeric_columns:
            if col in df.columns:
                summary[f"{col}_总计"] = df[col].sum()
                summary[f"{col}_平均"] = round(df[col].mean(), 2)
                summary[f"{col}_最大"] = df[col].max()
                summary[f"{col}_最小"] = df[col].min()
        
        return summary
    
    def _get_fields_description(self, columns: List[str], table_name: str = None) -> Dict[str, str]:
        """获取字段说明"""
        descriptions = {}
        
        for col in columns:
            if table_name and table_name in self.field_mappings:
                # 反向查找原始字段名
                original_field = None
                for orig, trans in self.field_mappings[table_name].items():
                    if trans == col:
                        original_field = orig
                        break
                
                if original_field:
                    descriptions[col] = f"{col} (原字段: {original_field})"
                else:
                    descriptions[col] = col
            else:
                descriptions[col] = col
        
        return descriptions
    
    def format_simple_result(self, result: str, query_type: str = None) -> str:
        """格式化简单查询结果"""
        try:
            # 如果结果包含表格，尝试解析并格式化
            if "| " in result and "---|" in result:
                lines = result.split('\n')
                table_lines = []
                other_lines = []
                
                in_table = False
                for line in lines:
                    if "| " in line:
                        in_table = True
                        table_lines.append(line)
                    elif in_table and "---|" in line:
                        table_lines.append(line)
                    elif in_table and line.strip() == "":
                        in_table = False
                        other_lines.append(line)
                    else:
                        other_lines.append(line)
                
                if table_lines:
                    # 格式化表格
                    formatted_table = self._format_markdown_table(table_lines)
                    return '\n'.join(other_lines[:2]) + '\n\n' + formatted_table + '\n\n' + '\n'.join(other_lines[2:])
            
            return result
            
        except Exception as e:
            logger.error(f"简单结果格式化失败: {e}")
            return result
    
    def _format_markdown_table(self, table_lines: List[str]) -> str:
        """格式化Markdown表格"""
        if len(table_lines) < 2:
            return '\n'.join(table_lines)
        
        # 解析表头
        header_line = table_lines[0]
        headers = [h.strip() for h in header_line.split('|')[1:-1]]
        
        # 翻译表头
        translated_headers = []
        for header in headers:
            # 尝试从字段映射中找到对应的中文名
            found = False
            for table_name, mappings in self.field_mappings.items():
                if header in mappings:
                    translated_headers.append(mappings[header])
                    found = True
                    break
            if not found:
                translated_headers.append(header)
        
        # 重建表格
        new_table_lines = []
        new_table_lines.append('| ' + ' | '.join(translated_headers) + ' |')
        new_table_lines.append('|' + '---|' * len(translated_headers))
        
        # 添加数据行
        for line in table_lines[2:]:
            if '| ' in line:
                new_table_lines.append(line)
        
        return '\n'.join(new_table_lines)
