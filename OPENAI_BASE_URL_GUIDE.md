# 🌐 OpenAI Base URL 配置指南

## 📋 概述

项目现已支持自定义 OpenAI API 的 base_url，这允许您：
- 使用 OpenAI 代理服务
- 使用兼容 OpenAI API 的第三方服务
- 通过代理访问 OpenAI API
- 使用本地部署的 OpenAI 兼容模型

## ⚙️ 配置方法

### 1. 环境变量配置

在 `.env` 文件中添加：

```ini
# OpenAI API 配置
OPENAI_API_KEY="sk-your-api-key"
OPENAI_BASE_URL="https://your-custom-endpoint.com/v1"
```

### 2. 常见配置示例

#### 官方 OpenAI API（默认）
```ini
OPENAI_API_KEY="sk-your-openai-key"
OPENAI_BASE_URL="https://api.openai.com/v1"  # 可省略，这是默认值
```

#### 使用代理服务
```ini
OPENAI_API_KEY="sk-your-openai-key"
OPENAI_BASE_URL="https://your-proxy.com/v1"
```

#### 使用第三方兼容服务
```ini
OPENAI_API_KEY="your-service-key"
OPENAI_BASE_URL="https://api.your-service.com/v1"
```

#### 本地部署的模型
```ini
OPENAI_API_KEY="local-key"  # 某些本地服务可能不需要真实密钥
OPENAI_BASE_URL="http://localhost:8000/v1"
```

## 🔧 支持的组件

### 1. CrewAI 智能体
所有智能体（数据分析师、报告撰写人等）都会使用自定义的 base_url：

```python
# 自动使用配置的 base_url
analyst = create_data_analyst_agent(model_provider="openai")
writer = create_report_writer_agent(model_provider="openai")
```

### 2. Vanna 训练器
Vanna 训练过程也会使用自定义的 base_url：

```bash
# 训练时会自动使用配置的 base_url
python train_vanna.py --config training_config.json --verbose
```

## 🧪 测试配置

### 1. 检查配置状态
```bash
python check_vanna_config.py
```

输出示例：
```
🔑 API 密钥检查:
  ✅ OPENAI_API_KEY: 已配置 (sk-...abcd)
  🌐 OPENAI_BASE_URL: https://your-proxy.com/v1
```

### 2. 测试连接
```bash
python train_vanna.py --info --verbose
```

### 3. 测试应用
```bash
python run.py
```

## 🚨 故障排除

### 常见问题

1. **连接超时**
   ```
   错误：Connection timeout
   解决：检查 base_url 是否正确，网络是否可达
   ```

2. **认证失败**
   ```
   错误：401 Unauthorized
   解决：检查 API 密钥是否正确，是否适用于该服务
   ```

3. **API 格式不兼容**
   ```
   错误：Invalid response format
   解决：确认服务完全兼容 OpenAI API 格式
   ```

### 调试步骤

1. **验证 URL 可访问性**
   ```bash
   curl -X GET "https://your-proxy.com/v1/models" \
        -H "Authorization: Bearer sk-your-key"
   ```

2. **检查环境变量**
   ```bash
   python -c "import os; print('Base URL:', os.getenv('OPENAI_BASE_URL'))"
   ```

3. **启用详细日志**
   ```bash
   python train_vanna.py --info --verbose
   ```

## 🌟 使用场景

### 1. 网络代理
当直接访问 OpenAI API 受限时：
```ini
OPENAI_BASE_URL="https://openai-proxy.your-company.com/v1"
```

### 2. 成本优化
使用更便宜的兼容服务：
```ini
OPENAI_BASE_URL="https://api.cheaper-service.com/v1"
```

### 3. 本地部署
使用本地部署的大模型：
```ini
OPENAI_BASE_URL="http://localhost:8000/v1"
```

### 4. 多区域部署
使用不同区域的服务：
```ini
OPENAI_BASE_URL="https://api-asia.openai-compatible.com/v1"
```

## 📊 配置验证

运行配置检查脚本验证设置：

```bash
python check_vanna_config.py
```

成功配置的输出：
```
🔍 检查 Vanna 训练配置
==================================================
🔑 API 密钥检查:
  ✅ OPENAI_API_KEY: 已配置 (sk-...1234)
  🌐 OPENAI_BASE_URL: https://your-proxy.com/v1

🗄️ 数据库配置检查:
  ✅ MYSQL_HOST: your-host.com
  ✅ MYSQL_USER: your_user
  ✅ MYSQL_PASSWORD: 已配置
  ✅ MYSQL_DATABASE: your_database

📊 配置状态总结:
  Vanna 训练就绪: ✅ 是
  应用运行就绪: ✅ 是

💡 建议:
  🎉 配置完整！可以开始训练:
     python train_vanna.py --config training_config.json --verbose
  🚀 可以启动应用:
     python run.py
```

## 🔒 安全注意事项

1. **API 密钥保护**
   - 不要在代码中硬编码 API 密钥
   - 使用 `.env` 文件并添加到 `.gitignore`

2. **HTTPS 使用**
   - 生产环境务必使用 HTTPS
   - 避免在不安全的网络中传输 API 密钥

3. **访问控制**
   - 限制 API 密钥的权限范围
   - 定期轮换 API 密钥

现在您可以灵活配置 OpenAI API 的访问方式，适应各种网络环境和服务需求！
