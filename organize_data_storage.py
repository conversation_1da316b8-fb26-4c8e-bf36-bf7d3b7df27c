"""
整理数据存储结构
将缓存数据和训练数据移动到专门的文件夹中
"""

import os
import shutil
import glob
from pathlib import Path


def create_data_directories():
    """创建数据存储目录结构"""
    print("📁 创建数据存储目录结构...")
    
    directories = [
        "data",                          # 主数据目录
        "data/cache",                    # 缓存数据
        "data/cache/chromadb",          # ChromaDB 缓存
        "data/cache/python",            # Python 缓存
        "data/training",                # 训练数据
        "data/training/vanna",          # Vanna 训练数据
        "data/training/models",         # 模型文件
        "data/logs",                    # 日志文件
        "data/temp",                    # 临时文件
        "data/exports"                  # 导出数据
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"  ✅ 创建目录: {directory}")
    
    # 创建 .gitignore 文件
    gitignore_content = """# 数据目录 - 不提交到版本控制
/data/cache/
/data/temp/
/data/logs/
*.log
*.sqlite3
*.lock
*.bin
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
*.so
.coverage
.pytest_cache/
"""
    
    with open("data/.gitignore", "w", encoding="utf-8") as f:
        f.write(gitignore_content)
    
    print("  ✅ 创建 data/.gitignore")


def move_chromadb_files():
    """移动 ChromaDB 相关文件（安全模式）"""
    print("\n🗄️ 处理 ChromaDB 文件...")

    # 处理 ChromaDB 数据库文件
    chromadb_files = glob.glob("*.sqlite3")
    for file in chromadb_files:
        if os.path.exists(file):
            dest = f"data/cache/chromadb/{file}"
            try:
                # 先尝试复制，如果成功再删除原文件
                shutil.copy2(file, dest)
                print(f"  ✅ 复制: {file} -> {dest}")
                print(f"  ⚠️ 原文件保留: {file} (请手动删除)")
            except Exception as e:
                print(f"  ❌ 处理失败 {file}: {e}")

    # 处理 ChromaDB 锁文件
    lock_files = glob.glob("chromadb-*.lock")
    for file in lock_files:
        if os.path.exists(file):
            dest = f"data/cache/chromadb/{file}"
            try:
                shutil.copy2(file, dest)
                print(f"  ✅ 复制: {file} -> {dest}")
                print(f"  ⚠️ 原文件保留: {file} (请手动删除)")
            except Exception as e:
                print(f"  ❌ 处理失败 {file}: {e}")

    # 处理 ChromaDB 数据目录
    chromadb_dirs = glob.glob("*-*-*-*-*")  # UUID 格式的目录
    for dir_name in chromadb_dirs:
        if os.path.isdir(dir_name) and len(dir_name) == 36:  # UUID 长度
            dest = f"data/cache/chromadb/{dir_name}"
            try:
                shutil.copytree(dir_name, dest, dirs_exist_ok=True)
                print(f"  ✅ 复制目录: {dir_name} -> {dest}")
                print(f"  ⚠️ 原目录保留: {dir_name} (请手动删除)")
            except Exception as e:
                print(f"  ❌ 处理失败 {dir_name}: {e}")


def move_python_cache():
    """移动 Python 缓存文件"""
    print("\n🐍 移动 Python 缓存文件...")
    
    # 移动 __pycache__ 目录
    if os.path.exists("__pycache__"):
        dest = "data/cache/python/__pycache__"
        shutil.move("__pycache__", dest)
        print(f"  ✅ 移动: __pycache__ -> {dest}")


def move_training_files():
    """移动训练相关文件"""
    print("\n🎓 移动训练文件...")
    
    training_files = [
        "training_config.json",
        "train_vanna.py",
        "quick_train_vanna.py"
    ]
    
    for file in training_files:
        if os.path.exists(file):
            dest = f"data/training/{file}"
            shutil.move(file, dest)
            print(f"  ✅ 移动: {file} -> {dest}")


def move_test_files():
    """移动测试文件到专门目录"""
    print("\n🧪 整理测试文件...")
    
    # 创建测试目录
    os.makedirs("tests", exist_ok=True)
    
    test_files = glob.glob("test_*.py")
    test_files.extend(glob.glob("debug_*.py"))
    test_files.extend(glob.glob("check_*.py"))
    test_files.extend(glob.glob("fix_*.py"))
    test_files.extend(glob.glob("setup_*.py"))
    
    for file in test_files:
        if os.path.exists(file):
            dest = f"tests/{file}"
            shutil.move(file, dest)
            print(f"  ✅ 移动: {file} -> tests/{file}")


def move_documentation():
    """整理文档文件"""
    print("\n📚 整理文档文件...")
    
    # 创建文档目录
    os.makedirs("docs", exist_ok=True)
    
    doc_files = glob.glob("*_GUIDE.md")
    doc_files.extend(glob.glob("*_SUMMARY.md"))
    doc_files.extend(glob.glob("*_NOTES.md"))
    doc_files.extend(glob.glob("*_README.md"))
    doc_files.extend(["QUICK_START.md", "TIKTOK_TRAINING_README.md"])
    
    for file in doc_files:
        if os.path.exists(file):
            dest = f"docs/{file}"
            shutil.move(file, dest)
            print(f"  ✅ 移动: {file} -> docs/{file}")


def update_config_paths():
    """更新配置文件中的路径"""
    print("\n⚙️ 更新配置文件路径...")
    
    # 更新 Vanna 配置以使用新的数据目录
    config_updates = {
        "src/config.py": [
            ("chroma.sqlite3", "data/cache/chromadb/chroma.sqlite3"),
        ]
    }
    
    for config_file, replacements in config_updates.items():
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                for old_path, new_path in replacements:
                    if old_path in content:
                        content = content.replace(old_path, new_path)
                        print(f"  ✅ 更新 {config_file}: {old_path} -> {new_path}")
                
                with open(config_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                    
            except Exception as e:
                print(f"  ⚠️ 更新 {config_file} 失败: {e}")


def create_data_manager():
    """创建数据管理工具"""
    print("\n🔧 创建数据管理工具...")
    
    data_manager_content = '''"""
数据管理工具
管理缓存、训练数据和临时文件
"""

import os
import shutil
import glob
from pathlib import Path


class DataManager:
    """数据管理器"""
    
    def __init__(self):
        self.data_dir = Path("data")
        self.cache_dir = self.data_dir / "cache"
        self.training_dir = self.data_dir / "training"
        self.logs_dir = self.data_dir / "logs"
        self.temp_dir = self.data_dir / "temp"
        self.exports_dir = self.data_dir / "exports"
    
    def clean_cache(self):
        """清理缓存数据"""
        print("🧹 清理缓存数据...")
        
        cache_patterns = [
            self.cache_dir / "chromadb" / "*.sqlite3",
            self.cache_dir / "chromadb" / "*.lock",
            self.cache_dir / "python" / "__pycache__",
            self.temp_dir / "*"
        ]
        
        for pattern in cache_patterns:
            for item in glob.glob(str(pattern)):
                try:
                    if os.path.isfile(item):
                        os.remove(item)
                        print(f"  ✅ 删除文件: {item}")
                    elif os.path.isdir(item):
                        shutil.rmtree(item)
                        print(f"  ✅ 删除目录: {item}")
                except Exception as e:
                    print(f"  ❌ 删除失败 {item}: {e}")
    
    def backup_training_data(self, backup_name=None):
        """备份训练数据"""
        if not backup_name:
            from datetime import datetime
            backup_name = f"training_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        backup_dir = self.data_dir / "backups" / backup_name
        backup_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"💾 备份训练数据到: {backup_dir}")
        
        if self.training_dir.exists():
            shutil.copytree(self.training_dir, backup_dir / "training", dirs_exist_ok=True)
            print("  ✅ 训练数据备份完成")
        
        if (self.cache_dir / "chromadb").exists():
            shutil.copytree(self.cache_dir / "chromadb", backup_dir / "chromadb", dirs_exist_ok=True)
            print("  ✅ ChromaDB 数据备份完成")
    
    def get_storage_info(self):
        """获取存储信息"""
        print("📊 数据存储信息:")
        
        directories = [
            ("缓存数据", self.cache_dir),
            ("训练数据", self.training_dir),
            ("日志文件", self.logs_dir),
            ("临时文件", self.temp_dir),
            ("导出数据", self.exports_dir)
        ]
        
        for name, path in directories:
            if path.exists():
                size = sum(f.stat().st_size for f in path.rglob('*') if f.is_file())
                size_mb = size / (1024 * 1024)
                file_count = len(list(path.rglob('*')))
                print(f"  {name}: {size_mb:.2f} MB ({file_count} 个文件)")
            else:
                print(f"  {name}: 不存在")
    
    def ensure_directories(self):
        """确保所有必要目录存在"""
        directories = [
            self.cache_dir / "chromadb",
            self.cache_dir / "python",
            self.training_dir / "vanna",
            self.training_dir / "models",
            self.logs_dir,
            self.temp_dir,
            self.exports_dir
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)


def main():
    """主函数"""
    manager = DataManager()
    
    print("🗂️ 数据管理工具")
    print("=" * 30)
    
    # 确保目录存在
    manager.ensure_directories()
    
    # 显示存储信息
    manager.get_storage_info()
    
    print("\\n💡 可用命令:")
    print("  manager.clean_cache()           # 清理缓存")
    print("  manager.backup_training_data()  # 备份训练数据")
    print("  manager.get_storage_info()      # 查看存储信息")


if __name__ == "__main__":
    main()
'''
    
    with open("src/data_manager.py", "w", encoding="utf-8") as f:
        f.write(data_manager_content)
    
    print("  ✅ 创建 src/data_manager.py")


def create_readme():
    """创建数据目录说明文件"""
    print("\n📝 创建数据目录说明...")
    
    readme_content = """# 数据存储目录结构

本目录用于存储 TikTok 数据分析系统的各种数据文件。

## 目录结构

```
data/
├── cache/              # 缓存数据
│   ├── chromadb/      # ChromaDB 向量数据库缓存
│   └── python/        # Python 运行时缓存
├── training/          # 训练数据
│   ├── vanna/         # Vanna 模型训练数据
│   └── models/        # 训练好的模型文件
├── logs/              # 日志文件
├── temp/              # 临时文件
├── exports/           # 导出的数据文件
└── backups/           # 备份文件
```

## 文件说明

### cache/chromadb/
- `*.sqlite3`: ChromaDB 向量数据库文件
- `*.lock`: ChromaDB 锁文件
- `*-*-*-*-*`: UUID 格式的 ChromaDB 数据目录

### training/
- `training_config.json`: 训练配置文件
- `train_vanna.py`: Vanna 训练脚本
- `quick_train_vanna.py`: 快速训练脚本

### logs/
- 系统运行日志
- 错误日志
- 调试日志

### temp/
- 临时生成的文件
- 中间处理结果

### exports/
- 导出的查询结果
- 生成的报告文件

## 管理工具

使用 `src/data_manager.py` 来管理数据：

```python
from src.data_manager import DataManager

manager = DataManager()

# 查看存储信息
manager.get_storage_info()

# 清理缓存
manager.clean_cache()

# 备份训练数据
manager.backup_training_data()
```

## 注意事项

1. **不要手动删除** cache/chromadb/ 中的文件，这会导致 Vanna 需要重新训练
2. **定期备份** training/ 目录中的重要训练数据
3. **定期清理** temp/ 和 logs/ 目录中的旧文件
4. **版本控制**: 此目录中的大部分文件不应提交到 Git（已配置 .gitignore）

## 恢复数据

如果需要恢复数据：

1. 从备份中恢复 training/ 目录
2. 重新运行训练脚本生成缓存
3. 检查配置文件中的路径设置
"""
    
    with open("data/README.md", "w", encoding="utf-8") as f:
        f.write(readme_content)
    
    print("  ✅ 创建 data/README.md")


def main():
    """主整理函数"""
    print("🗂️ 整理数据存储结构")
    print("=" * 40)
    
    # 创建目录结构
    create_data_directories()
    
    # 移动各类文件
    move_chromadb_files()
    move_python_cache()
    move_training_files()
    move_test_files()
    move_documentation()
    
    # 更新配置
    update_config_paths()
    
    # 创建管理工具
    create_data_manager()
    create_readme()
    
    print("\n🎉 数据存储结构整理完成！")
    print("\n📁 新的目录结构:")
    print("  data/cache/chromadb/    # ChromaDB 缓存")
    print("  data/training/          # 训练数据")
    print("  data/logs/              # 日志文件")
    print("  data/temp/              # 临时文件")
    print("  tests/                  # 测试文件")
    print("  docs/                   # 文档文件")
    print("\n💡 使用数据管理工具:")
    print("  python -c \"from src.data_manager import DataManager; DataManager().get_storage_info()\"")


if __name__ == "__main__":
    main()
