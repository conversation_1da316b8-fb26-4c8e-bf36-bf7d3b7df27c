{"ddl": [{"sql": "CREATE TABLE `at_tiktok_author_pool` (\n  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',\n  `author_id` varchar(64) NOT NULL COMMENT '发布账号ID',\n  `unique_id` varchar(128) NOT NULL COMMENT '用户名',\n  `author_name` varchar(255) DEFAULT NULL COMMENT '作者昵称',\n  `author_avatar` varchar(512) DEFAULT NULL COMMENT '作者头像URL',\n  `sec_uid` varchar(255) DEFAULT NULL COMMENT '作者账号加密ID(secUid)',\n  `author_url` varchar(512) DEFAULT NULL COMMENT '发布者主页地址',\n  `desc` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '个人简介',\n  `register_time` int DEFAULT NULL COMMENT '账号创建时间戳',\n  `is_verified` tinyint(1) DEFAULT '0' COMMENT '是否认证(1:是,0:否)',\n  `region` varchar(32) DEFAULT NULL COMMENT '用户注册地区(如US)',\n  `language` varchar(32) DEFAULT NULL COMMENT '用户界面语言(如en)',\n  `private_account` tinyint(1) DEFAULT '0' COMMENT '是否私密账号(1:是,0:否)',\n  `commerce_user` tinyint(1) DEFAULT '0' COMMENT '是否开通电商功能(1:是,0:否)',\n  `tt_seller` tinyint(1) DEFAULT '0' COMMENT '是否TikTok小店卖家(1:是,0:否)',\n  `commerce_category` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '开通电商的类目',\n  `follower_count` int DEFAULT '0' COMMENT '粉丝数',\n  `following_count` int DEFAULT '0' COMMENT '关注数',\n  `heart_count` int DEFAULT '0' COMMENT '总获赞数',\n  `video_count` int DEFAULT '0' COMMENT '发布视频数',\n  `friend_count` int DEFAULT '0' COMMENT '好友数',\n  `update_status` int DEFAULT NULL COMMENT '更新状态',\n  `creator` bigint DEFAULT NULL COMMENT '创建者',\n  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '数据创建时间',\n  `updater` bigint DEFAULT NULL COMMENT '数据更新人',\n  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据更新时间',\n  `is_del` tinyint DEFAULT '0' COMMENT '是否删除（0未删除，1已删除）',\n  `status` tinyint DEFAULT NULL COMMENT 'status，0=未监控；1=监控中',\n  PRIMARY KEY (`id`),\n  UNIQUE KEY `uk_author_id` (`author_id`),\n  UNIQUE KEY `idx_unique_id` (`unique_id`) USING BTREE\n) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='TikTok达人信息表';", "description": "TikTok达人信息表结构"}, {"sql": "CREATE TABLE `at_tiktok_author_work_record` (\n  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',\n  `work_id` varchar(64) NOT NULL COMMENT '作品唯一标识',\n  `work_uuid` varchar(64) DEFAULT NULL COMMENT '三方生成唯一ID',\n  `author_id` varchar(64) NOT NULL COMMENT '发布账号ID',\n  `unique_id` varchar(128) NOT NULL COMMENT '作者唯一用户名',\n  `sec_uid` varchar(255) DEFAULT NULL COMMENT '作者账号加密ID',\n  `url` varchar(2048) DEFAULT NULL COMMENT '作品链接',\n  `category_type` int DEFAULT NULL COMMENT '分类类型(对应TikTok分类体系)',\n  `thumbnail_link` varchar(2048) DEFAULT NULL COMMENT '封面图链接',\n  `is_ad` tinyint DEFAULT '0' COMMENT '是否广告(1:是,0:否)',\n  `title` text COMMENT '作品标题',\n  `content` text COMMENT '作品内容描述',\n  `hashtags` varchar(1024) DEFAULT NULL COMMENT '话题标签数组，格式: [\"#tag1\",\"#tag2\"]',\n  `images` text COMMENT '图片URL数组，格式: [\"url1\",\"url2\"]',\n  `publish_time` int DEFAULT NULL COMMENT '发布时间戳',\n  `text_language` varchar(10) DEFAULT 'en' COMMENT '文本语言(ISO 639-1)',\n  `location_ip` varchar(100) DEFAULT '' COMMENT '发布地理位置',\n  `play_count` int DEFAULT '0' COMMENT '播放量',\n  `like_count` int DEFAULT '0' COMMENT '点赞数',\n  `comment_count` int DEFAULT '0' COMMENT '评论数',\n  `share_count` int DEFAULT '0' COMMENT '转发数',\n  `collect_count` int DEFAULT '0' COMMENT '收藏数',\n  `video_id` varchar(128) DEFAULT NULL COMMENT '视频ID',\n  `music_id` varchar(128) DEFAULT NULL COMMENT '音乐ID',\n  `product_ids` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '商品ID',\n  `creator` bigint DEFAULT NULL COMMENT '创建者',\n  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '数据创建时间',\n  `updater` bigint DEFAULT NULL COMMENT '数据更新人',\n  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '数据更新时间',\n  PRIMARY KEY (`id`),\n  UNIQUE KEY `uk_work_id` (`work_id`),\n  KEY `idx_author_id` (`author_id`),\n  KEY `idx_publish_time` (`publish_time`)\n) ENGINE=InnoDB AUTO_INCREMENT=376 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='TikTok达人作品主表';", "description": "TikTok达人作品主表结构"}], "sql_examples": [{"question": "查询粉丝数最多的10个达人", "sql": "SELECT unique_id, author_name, follower_count FROM at_tiktok_author_pool WHERE is_del = 0 ORDER BY follower_count DESC LIMIT 10;", "description": "热门达人查询"}, {"question": "统计上周发布的作品数量", "sql": "SELECT COUNT(*) as total_works FROM at_tiktok_author_work_record WHERE FROM_UNIXTIME(publish_time) >= DATE_SUB(NOW(), INTERVAL 7 DAY);", "description": "时间范围统计"}, {"question": "查询每个达人的总播放量", "sql": "SELECT a.unique_id, a.author_name, SUM(w.play_count) as total_plays FROM at_tiktok_author_pool a JOIN at_tiktok_author_work_record w ON a.author_id = w.author_id WHERE a.is_del = 0 GROUP BY a.author_id ORDER BY total_plays DESC;", "description": "聚合查询示例"}, {"question": "查找播放量超过100万的作品", "sql": "SELECT w.title, w.play_count, a.unique_id, a.author_name FROM at_tiktok_author_work_record w JOIN at_tiktok_author_pool a ON w.author_id = a.author_id WHERE w.play_count > 1000000 ORDER BY w.play_count DESC;", "description": "高播放量作品查询"}, {"question": "分析电商达人的平均粉丝数", "sql": "SELECT AVG(follower_count) as avg_followers FROM at_tiktok_author_pool WHERE commerce_user = 1 AND is_del = 0;", "description": "电商达人统计查询"}, {"question": "查询最近一个月互动率最高的作品", "sql": "SELECT w.title, w.play_count, w.like_count, w.comment_count, w.share_count, (w.like_count + w.comment_count + w.share_count) / w.play_count * 100 as engagement_rate FROM at_tiktok_author_work_record w WHERE FROM_UNIXTIME(w.publish_time) >= DATE_SUB(NOW(), INTERVAL 30 DAY) AND w.play_count > 0 ORDER BY engagement_rate DESC LIMIT 20;", "description": "互动率计算查询"}, {"question": "统计各地区达人的数量", "sql": "SELECT region, COUNT(*) as author_count FROM at_tiktok_author_pool WHERE is_del = 0 AND region IS NOT NULL GROUP BY region ORDER BY author_count DESC;", "description": "地区分布统计"}, {"question": "查询认证达人的作品表现", "sql": "SELECT a.unique_id, a.author_name, COUNT(w.work_id) as work_count, AVG(w.play_count) as avg_plays FROM at_tiktok_author_pool a JOIN at_tiktok_author_work_record w ON a.author_id = w.author_id WHERE a.is_verified = 1 AND a.is_del = 0 GROUP BY a.author_id ORDER BY avg_plays DESC;", "description": "认证达人分析"}, {"question": "分析广告作品的表现", "sql": "SELECT COUNT(*) as ad_count, AVG(play_count) as avg_plays, AVG(like_count) as avg_likes FROM at_tiktok_author_work_record WHERE is_ad = 1;", "description": "广告作品分析"}, {"question": "查询TikTok小店卖家的作品数据", "sql": "SELECT a.unique_id, a.author_name, a.commerce_category, COUNT(w.work_id) as work_count, SUM(w.play_count) as total_plays FROM at_tiktok_author_pool a JOIN at_tiktok_author_work_record w ON a.author_id = w.author_id WHERE a.tt_seller = 1 AND a.is_del = 0 GROUP BY a.author_id ORDER BY total_plays DESC;", "description": "TikTok小店卖家分析"}, {"question": "分析最近发布的热门话题标签", "sql": "SELECT hashtags, COUNT(*) as usage_count, AVG(play_count) as avg_plays FROM at_tiktok_author_work_record WHERE FROM_UNIXTIME(publish_time) >= DATE_SUB(NOW(), INTERVAL 7 DAY) AND hashtags IS NOT NULL AND hashtags != '' GROUP BY hashtags ORDER BY usage_count DESC LIMIT 20;", "description": "热门话题标签分析"}, {"question": "查询粉丝数和获赞数比例最高的达人", "sql": "SELECT unique_id, author_name, follower_count, heart_count, (heart_count / follower_count) as like_ratio FROM at_tiktok_author_pool WHERE follower_count > 1000 AND is_del = 0 ORDER BY like_ratio DESC LIMIT 10;", "description": "达人影响力分析"}], "documentation": [{"content": "at_tiktok_author_pool 表存储 TikTok 达人的基本信息。author_id 是发布账号ID，unique_id 是用户名，author_name 是昵称。follower_count 是粉丝数，heart_count 是总获赞数，video_count 是发布视频数。is_verified 表示是否认证(1:是,0:否)。", "description": "达人信息表说明"}, {"content": "at_tiktok_author_work_record 表存储达人作品信息。work_id 是作品唯一标识，通过 author_id 与达人表关联。play_count 是播放量，like_count 是点赞数，comment_count 是评论数，share_count 是转发数，collect_count 是收藏数。", "description": "作品记录表说明"}, {"content": "电商相关字段：commerce_user 表示是否开通电商功能(1:是,0:否)，tt_seller 表示是否TikTok小店卖家(1:是,0:否)，commerce_category 是开通电商的类目。这些字段用于分析电商达人。", "description": "电商功能字段说明"}, {"content": "时间字段说明：register_time 和 publish_time 是时间戳格式，需要使用 FROM_UNIXTIME() 函数转换。create_time 和 update_time 是 datetime 格式，可以直接使用。", "description": "时间字段格式说明"}, {"content": "互动率计算公式：(like_count + comment_count + share_count + collect_count) / play_count * 100。这是衡量作品内容质量和用户参与度的重要指标。", "description": "互动率计算说明"}, {"content": "hashtags 字段存储话题标签数组，格式为 JSON 字符串：[\"#tag1\",\"#tag2\"]。分析时可以使用 JSON 函数或字符串匹配来处理。", "description": "话题标签字段说明"}, {"content": "地区和语言字段：region 表示用户注册地区(如US、CN)，language 表示用户界面语言(如en、zh)，text_language 表示作品文本语言。", "description": "地区语言字段说明"}, {"content": "状态字段：is_del 表示是否删除(0:未删除,1:已删除)，status 表示监控状态(0:未监控,1:监控中)，private_account 表示是否私密账号(1:是,0:否)。", "description": "状态字段说明"}], "plans": [{"question": "分析达人的内容策略和表现", "plan": "1. 从 at_tiktok_author_pool 查询达人的基本信息(粉丝数、获赞数、是否认证等)\n2. 从 at_tiktok_author_work_record 统计该达人的作品数量和平均表现\n3. 分析作品的播放量、点赞数、评论数分布\n4. 提取和分析常用的话题标签\n5. 计算平均互动率和内容质量指标\n6. 对比同地区或同类型其他达人的表现", "description": "达人内容策略分析计划"}, {"question": "研究热门作品的特征和规律", "plan": "1. 筛选高播放量作品(play_count > 阈值)\n2. 分析这些作品的发布时间规律\n3. 统计热门话题标签和内容类型\n4. 计算互动率指标(点赞率、评论率、分享率)\n5. 分析是否为广告作品的影响\n6. 研究达人认证状态与作品表现的关系", "description": "热门作品特征分析计划"}, {"question": "电商达人和TikTok小店分析", "plan": "1. 筛选电商达人(commerce_user=1)和TikTok小店卖家(tt_seller=1)\n2. 分析电商达人的粉丝规模和获赞情况\n3. 统计不同电商类目的达人数量和表现\n4. 对比电商达人与普通达人的作品表现差异\n5. 分析电商达人的内容策略和话题标签使用\n6. 研究电商功能对达人影响力的影响", "description": "电商达人分析计划"}, {"question": "地区和语言分布分析", "plan": "1. 统计不同地区(region)达人的数量分布\n2. 分析各地区达人的平均粉丝数和影响力\n3. 研究不同语言(language, text_language)内容的表现\n4. 对比不同地区达人的内容偏好和话题标签\n5. 分析跨地区内容的传播效果\n6. 研究地区文化对内容表现的影响", "description": "地区语言分析计划"}]}