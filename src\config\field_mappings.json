{"field_mappings": {"at_tiktok_author_pool": {"id": "ID", "author_id": "达人ID", "unique_id": "用户名", "author_name": "达人昵称", "author_avatar": "头像链接", "sec_uid": "加密ID", "author_url": "主页地址", "desc": "个人简介", "register_time": "注册时间", "is_verified": "认证状态", "region": "注册地区", "language": "界面语言", "private_account": "私密账号", "commerce_user": "电商功能", "tt_seller": "小店卖家", "commerce_category": "电商类目", "follower_count": "粉丝数", "following_count": "关注数", "heart_count": "总获赞数", "video_count": "发布视频数", "friend_count": "好友数", "update_status": "更新状态", "creator": "创建者", "create_time": "创建时间", "updater": "更新人", "update_time": "更新时间", "is_del": "删除状态", "status": "监控状态"}, "at_tiktok_author_work_record": {"id": "ID", "work_id": "作品ID", "work_uuid": "作品UUID", "author_id": "达人ID", "unique_id": "用户名", "sec_uid": "加密ID", "url": "作品链接", "category_type": "分类类型", "thumbnail_link": "封面图", "is_ad": "广告标识", "title": "作品标题", "content": "作品描述", "hashtags": "话题标签", "images": "图片链接", "publish_time": "发布时间", "text_language": "文本语言", "location_ip": "发布位置", "play_count": "播放量", "like_count": "点赞数", "comment_count": "评论数", "share_count": "分享数", "collect_count": "收藏数", "video_id": "视频ID", "music_id": "音乐ID", "product_ids": "商品ID", "creator": "创建者", "create_time": "创建时间", "updater": "更新人", "update_time": "更新时间"}}, "data_enhancers": {"达人基本信息": {"required_fields": ["unique_id", "author_name", "follower_count", "heart_count", "video_count"], "optional_fields": ["author_avatar", "desc", "region", "is_verified", "commerce_user"], "description": "达人的基本信息，包括用户名、昵称、粉丝数等关键指标", "auto_enhance_keywords": ["达人", "用户", "账号", "博主"]}, "作品基本信息": {"required_fields": ["title", "play_count", "like_count", "comment_count", "share_count"], "optional_fields": ["thumbnail_link", "hashtags", "publish_time", "is_ad"], "description": "作品的基本信息，包括标题、播放量、互动数据等", "auto_enhance_keywords": ["作品", "视频", "内容", "发布"]}, "互动数据": {"required_fields": ["play_count", "like_count", "comment_count", "share_count", "collect_count"], "calculated_fields": ["engagement_rate", "like_rate", "comment_rate"], "description": "作品的互动数据和计算指标", "auto_enhance_keywords": ["互动", "点赞", "评论", "分享", "播放"]}, "电商数据": {"required_fields": ["commerce_user", "tt_seller", "commerce_category"], "optional_fields": ["product_ids"], "description": "达人的电商相关数据", "auto_enhance_keywords": ["电商", "商品", "卖家", "带货"]}, "地区分析": {"required_fields": ["region", "language"], "optional_fields": ["text_language", "location_ip"], "description": "地区和语言相关数据", "auto_enhance_keywords": ["地区", "区域", "语言", "位置"]}}, "value_formatters": {"boolean_fields": {"is_verified": {"1": "已认证", "0": "未认证"}, "private_account": {"1": "私密账号", "0": "公开账号"}, "commerce_user": {"1": "已开通", "0": "未开通"}, "tt_seller": {"1": "是", "0": "否"}, "is_ad": {"1": "广告", "0": "非广告"}, "is_del": {"1": "已删除", "0": "正常"}, "status": {"1": "监控中", "0": "未监控"}}, "number_fields": {"follower_count": {"format": "number", "unit": "人", "threshold": 10000}, "following_count": {"format": "number", "unit": "人", "threshold": 1000}, "heart_count": {"format": "number", "unit": "个", "threshold": 10000}, "video_count": {"format": "number", "unit": "个", "threshold": 100}, "play_count": {"format": "number", "unit": "次", "threshold": 10000}, "like_count": {"format": "number", "unit": "个", "threshold": 1000}, "comment_count": {"format": "number", "unit": "个", "threshold": 100}, "share_count": {"format": "number", "unit": "个", "threshold": 100}, "collect_count": {"format": "number", "unit": "个", "threshold": 100}}, "time_fields": {"register_time": {"type": "timestamp", "format": "%Y-%m-%d %H:%M:%S"}, "publish_time": {"type": "timestamp", "format": "%Y-%m-%d %H:%M:%S"}, "create_time": {"type": "datetime", "format": "%Y-%m-%d %H:%M:%S"}, "update_time": {"type": "datetime", "format": "%Y-%m-%d %H:%M:%S"}}}, "query_templates": {"达人排行": {"description": "查询达人排行榜时自动补充的字段", "auto_fields": ["unique_id", "author_name", "follower_count", "heart_count", "video_count", "region", "is_verified"], "sort_field": "follower_count", "limit": 20}, "作品排行": {"description": "查询作品排行榜时自动补充的字段", "auto_fields": ["title", "unique_id", "author_name", "play_count", "like_count", "comment_count", "share_count", "publish_time"], "sort_field": "play_count", "limit": 20}, "互动分析": {"description": "分析互动数据时自动补充的字段", "auto_fields": ["title", "unique_id", "author_name", "play_count", "like_count", "comment_count", "share_count", "collect_count"], "calculated_fields": ["engagement_rate", "like_rate"], "limit": 50}}, "display_rules": {"table_max_rows": 20, "description_max_length": 100, "title_max_length": 50, "show_summary": true, "show_field_descriptions": true, "number_format": "chinese", "date_format": "chinese"}}