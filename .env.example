# 大模型 API Keys
OPENAI_API_KEY="sk-..."
OPENAI_BASE_URL="https://api.openai.com/v1"  # OpenAI API 基础URL，可修改为代理地址
# GROQ_API_KEY="gsk_..." # 如果使用Groq
# ANTHROPIC_API_KEY="claude-..." # 如果使用Anthropic
# DASHSCOPE_API_KEY="sk-..." # 阿里云通义系列模型

# 数据库连接信息
MYSQL_HOST="localhost"
MYSQL_USER="root"
MYSQL_PASSWORD="your_password"
MYSQL_PORT="3306"
MYSQL_DATABASE="tiktok_data"

# 数据库连接超时配置（可选）
MYSQL_CONNECTION_TIMEOUT="60"  # 连接超时时间（秒）
MYSQL_POOL_SIZE="5"           # 连接池大小

# Vanna 配置
VANNA_MODEL="chinook"  # Vanna 模型名称
VANNA_API_KEY=""  # 如果使用 Vanna Cloud
