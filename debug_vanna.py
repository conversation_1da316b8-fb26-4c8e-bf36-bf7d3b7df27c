"""
调试 Vanna 连接问题
"""
import os
import traceback
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def debug_config():
    """调试配置"""
    print("🔍 调试配置信息")
    print("=" * 50)
    
    try:
        from src.config import DATABASE_CONFIG, VANNA_CONFIG
        
        print("📊 数据库配置:")
        for key, value in DATABASE_CONFIG.items():
            if key == 'password':
                print(f"  {key}: {'*' * len(str(value)) if value else 'None'}")
            else:
                print(f"  {key}: {value}")
        
        print(f"\n🤖 Vanna 配置:")
        for key, value in VANNA_CONFIG.items():
            if 'key' in key.lower():
                print(f"  {key}: {'*' * len(str(value)) if value else 'None'}")
            else:
                print(f"  {key}: {value}")
        
        print(f"\n🔑 环境变量:")
        print(f"  DASHSCOPE_API_KEY: {'已设置' if os.getenv('DASHSCOPE_API_KEY') else '未设置'}")
        print(f"  OPENAI_API_KEY: {'已设置' if os.getenv('OPENAI_API_KEY') else '未设置'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置调试失败: {e}")
        traceback.print_exc()
        return False

def debug_vanna_setup():
    """调试 Vanna 设置"""
    print("\n🧪 调试 Vanna 设置")
    print("=" * 50)
    
    try:
        # 测试导入
        print("📦 测试导入...")
        import vanna as vn
        print("  ✅ vanna 导入成功")
        
        # 测试通义千问导入
        try:
            from langchain_community.chat_models import ChatTongyi
            print("  ✅ ChatTongyi 导入成功")
        except ImportError as e:
            print(f"  ⚠️ ChatTongyi 导入失败: {e}")
        
        # 测试 Vanna 基类
        try:
            from vanna.base import VannaBase
            print("  ✅ VannaBase 导入成功")
        except ImportError as e:
            print(f"  ❌ VannaBase 导入失败: {e}")
        
        # 测试 OpenAI 本地模式
        try:
            from vanna.local import LocalContext_OpenAI
            print("  ✅ LocalContext_OpenAI 导入成功")
        except ImportError as e:
            print(f"  ❌ LocalContext_OpenAI 导入失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Vanna 设置调试失败: {e}")
        traceback.print_exc()
        return False

def debug_database_connection():
    """调试数据库连接"""
    print("\n🗄️ 调试数据库连接")
    print("=" * 50)
    
    try:
        from src.config import DATABASE_CONFIG
        
        # 检查配置完整性
        required_fields = ['host', 'database', 'user', 'password']
        missing_fields = []
        
        for field in required_fields:
            if not DATABASE_CONFIG.get(field):
                missing_fields.append(field)
        
        if missing_fields:
            print(f"❌ 缺少必要的数据库配置字段: {missing_fields}")
            return False
        
        # 检查默认值
        if DATABASE_CONFIG["password"] == "your_password":
            print("❌ 数据库密码仍为默认值，请在 .env 文件中设置正确的密码")
            return False
        
        # 测试数据库连接
        print("🔌 测试数据库连接...")
        import mysql.connector
        
        connection = mysql.connector.connect(
            host=DATABASE_CONFIG['host'],
            database=DATABASE_CONFIG['database'],
            user=DATABASE_CONFIG['user'],
            password=DATABASE_CONFIG['password'],
            port=DATABASE_CONFIG['port']
        )
        
        if connection.is_connected():
            cursor = connection.cursor()
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            print(f"  ✅ 数据库连接成功！MySQL 版本: {version[0]}")
            
            # 测试表是否存在
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            print(f"  📋 数据库中的表: {len(tables)} 个")
            for table in tables:
                print(f"    - {table[0]}")
            
            cursor.close()
            connection.close()
            return True
        else:
            print("❌ 数据库连接失败")
            return False
            
    except Exception as e:
        print(f"❌ 数据库连接调试失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主调试函数"""
    print("🐛 Vanna 连接问题调试工具")
    print("=" * 60)
    
    success_count = 0
    total_tests = 3
    
    # 调试配置
    if debug_config():
        success_count += 1
    
    # 调试 Vanna 设置
    if debug_vanna_setup():
        success_count += 1
    
    # 调试数据库连接
    if debug_database_connection():
        success_count += 1
    
    print(f"\n📊 调试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！可以尝试运行 Vanna 训练")
    else:
        print("⚠️ 存在问题，请根据上述信息进行修复")

if __name__ == "__main__":
    main()
