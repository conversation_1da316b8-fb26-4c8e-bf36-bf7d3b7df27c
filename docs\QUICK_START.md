# 🚀 快速开始指南

## 📋 前置要求

- Python 3.8+
- MySQL 数据库（用于存储 TikTok 数据）
- OpenAI API 密钥

## ⚡ 快速安装

### 1. 安装依赖

```bash
# 创建虚拟环境
python -m venv .venv

# 激活虚拟环境 (Windows)
.venv\Scripts\activate

# 激活虚拟环境 (Linux/Mac)
source .venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置环境变量

编辑 `.env` 文件，添加您的配置：

```ini
# 必需配置
OPENAI_API_KEY="sk-your-openai-api-key"
MYSQL_HOST="localhost"
MYSQL_USER="root"
MYSQL_PASSWORD="your_password"
MYSQL_DATABASE="tiktok_data"
```

### 3. 测试组件

```bash
# 运行组件测试
python test_components.py
```

### 4. 启动应用

```bash
# 方式一：使用启动脚本（推荐）
python run.py

# 方式二：直接启动
chainlit run app.py -w
```

## 🎯 使用示例

启动后在浏览器中访问 `http://localhost:8000`，然后输入：

### 基础查询
- "分析上周粉丝增长最快的达人"
- "查询播放量超过100万的视频"
- "最近一个月的热门话题"

### 深度分析
- "深度模式 分析美妆类达人的内容策略"
- "enhanced 查询用户互动率最高的视频类型"

## 🔧 故障排除

### 常见问题

1. **依赖安装失败**
   ```bash
   pip install --upgrade pip
   pip install -r requirements.txt
   ```

2. **API 密钥错误**
   - 检查 `.env` 文件中的 `OPENAI_API_KEY`
   - 确认 API 密钥有效且有足够额度

3. **数据库连接失败**
   - 确认 MySQL 服务正在运行
   - 检查数据库连接参数
   - 确认数据库和表已创建

4. **模块导入错误**
   - 确认虚拟环境已激活
   - 重新安装依赖包

### 获取帮助

- 在应用中输入 "help" 或 "帮助"
- 查看 `README.md` 获取详细文档
- 运行 `python test_components.py` 诊断问题

## 📊 数据库设置

如果您还没有 TikTok 数据库，可以使用以下示例结构：

```sql
-- 创建数据库
CREATE DATABASE tiktok_data;
USE tiktok_data;

-- 创建达人表
CREATE TABLE creators (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(100) UNIQUE,
    display_name VARCHAR(200),
    followers_count INT DEFAULT 0,
    following_count INT DEFAULT 0,
    likes_count BIGINT DEFAULT 0,
    videos_count INT DEFAULT 0,
    category VARCHAR(50),
    verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建视频表
CREATE TABLE videos (
    id INT PRIMARY KEY AUTO_INCREMENT,
    creator_id INT,
    title TEXT,
    description TEXT,
    view_count BIGINT DEFAULT 0,
    like_count INT DEFAULT 0,
    comment_count INT DEFAULT 0,
    share_count INT DEFAULT 0,
    duration INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (creator_id) REFERENCES creators(id)
);

-- 插入示例数据
INSERT INTO creators (username, display_name, followers_count, category) VALUES
('user1', '美妆达人小王', 150000, '美妆'),
('user2', '搞笑博主老李', 300000, '娱乐'),
('user3', '科技评测师', 80000, '科技');
```

## 🎉 开始使用

现在您可以开始使用 TikTok 数据分析智能体了！

记住：
- 问题越具体，分析结果越准确
- 使用"深度模式"获得更详细的分析
- 可以连续提问进行深入分析
