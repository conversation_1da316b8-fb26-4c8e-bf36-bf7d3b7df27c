"""
项目启动脚本
"""
import os
import sys
import subprocess
from src.config import validate_config

def check_dependencies():
    """检查依赖是否已安装"""
    try:
        import crewai
        import chainlit
        import vanna
        import mysql.connector
        print("✅ 所有依赖已正确安装")
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请运行: pip install -r requirements.txt")
        return False

def check_config():
    """检查配置是否正确"""
    try:
        validate_config()
        print("✅ 配置验证通过")
        return True
    except Exception as e:
        print(f"❌ 配置验证失败: {e}")
        print("请检查 .env 文件是否正确配置")
        return False

def main():
    """主启动函数"""
    print("🚀 TikTok 数据分析智能体启动检查...")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 检查配置
    if not check_config():
        print("\n💡 提示: 请编辑 .env 文件，添加您的 API 密钥和数据库配置")
        sys.exit(1)
    
    print("\n🎉 所有检查通过！正在启动应用...")
    print("🌐 应用将在 http://localhost:8000 启动")
    print("📝 按 Ctrl+C 停止应用")
    print("=" * 50)
    
    # 启动 Chainlit 应用
    try:
        subprocess.run(["chainlit", "run", "app.py", "-w"], check=True)
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
    except subprocess.CalledProcessError as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
