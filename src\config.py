"""
配置模块 - 管理环境变量和模型配置
"""

import os
from dotenv import load_dotenv
from langchain_openai import ChatOpenAI

# 加载环境变量
load_dotenv()

# 尝试导入通义模型
try:
    from langchain_community.llms import Tongyi
    from langchain_community.chat_models import ChatTongyi

    TONGYI_AVAILABLE = True
except ImportError:
    TONGYI_AVAILABLE = False

# 数据库配置
DATABASE_CONFIG = {
    "host": os.getenv("MYSQL_HOST", "localhost"),
    "user": os.getenv("MYSQL_USER", "root"),
    "password": os.getenv("MYSQL_PASSWORD", ""),
    "port": int(os.getenv("MYSQL_PORT", 3306)),
    "database": os.getenv("MYSQL_DATABASE", "tiktok_data"),
    # 连接超时配置
    "connection_timeout": int(
        os.getenv("MYSQL_CONNECTION_TIMEOUT", 60)
    ),  # 连接超时 60秒
    "autocommit": True,  # 自动提交，避免事务锁定
    "charset": "utf8mb4",  # 字符集
    "use_unicode": True,  # 使用 Unicode
    # 连接池配置
    "pool_name": "vanna_pool",
    "pool_size": int(os.getenv("MYSQL_POOL_SIZE", 5)),  # 连接池大小
    "pool_reset_session": True,  # 重置会话
}

# Vanna 专用数据库配置（用于 connect_to_mysql）
VANNA_DB_CONFIG = {
    "host": DATABASE_CONFIG["host"],
    "dbname": DATABASE_CONFIG["database"],
    "user": DATABASE_CONFIG["user"],
    "password": DATABASE_CONFIG["password"],
    "port": DATABASE_CONFIG["port"],
}

# Vanna 配置
VANNA_CONFIG = {
    "model": os.getenv("VANNA_MODEL", "chinook"),
    "api_key": os.getenv("VANNA_API_KEY", ""),
}


def get_llm_by_role(role: str, model_provider: str = "auto"):
    """
    根据角色返回不同的 LLM 实例
    实现按角色分配不同模型的需求

    Args:
        role (str): 角色名称 ('data_analyst', 'report_writer', 等)
        model_provider (str): 模型提供商 ('openai', 'tongyi', 'auto')

    Returns:
        LLM实例
    """
    # 自动选择可用的模型提供商
    if model_provider == "auto":
        if os.getenv("DASHSCOPE_API_KEY") and TONGYI_AVAILABLE:
            model_provider = "tongyi"
        elif os.getenv("OPENAI_API_KEY"):
            model_provider = "openai"
        else:
            raise ValueError("未找到可用的模型 API 密钥")

    # 使用通义模型
    if model_provider == "tongyi":
        dashscope_api_key = os.getenv("DASHSCOPE_API_KEY")
        if not dashscope_api_key:
            raise ValueError("DASHSCOPE_API_KEY 环境变量未设置")

        if not TONGYI_AVAILABLE:
            raise ValueError("通义模型依赖未安装，请运行: pip install dashscope")

        # 设置环境变量供 dashscope 使用
        os.environ["DASHSCOPE_API_KEY"] = dashscope_api_key

        if role == "data_analyst":
            # 数据分析师使用 qwen-plus（速度快、精确度高）
            return ChatTongyi(
                model_name="qwen-plus",
                temperature=0.1,  # 低温度确保分析的准确性
            )
        elif role == "report_writer":
            # 报告撰写人使用 qwen-max（能力最强、创造性好）
            return ChatTongyi(
                model_name="qwen-max",
                temperature=0.7,  # 较高温度增加创造性
            )
        else:
            # 默认使用 qwen-turbo（平衡性能和成本）
            return ChatTongyi(
                model_name="qwen-turbo",
                temperature=0.5,
            )

    # 使用 OpenAI 模型
    elif model_provider == "openai":
        api_key = os.getenv("OPENAI_API_KEY")
        base_url = os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1")

        if not api_key:
            raise ValueError("OPENAI_API_KEY 环境变量未设置")

        # 通用的 OpenAI 配置参数
        openai_config = {"api_key": api_key, "base_url": base_url}

        if role == "data_analyst":
            # 数据分析师使用速度快、精确度高的模型
            return ChatOpenAI(
                model_name="gpt-4o",
                temperature=0.1,  # 低温度确保分析的准确性
                **openai_config,
            )
        elif role == "report_writer":
            # 报告撰写人使用创造性更强的模型
            return ChatOpenAI(
                model_name="gpt-4-turbo",
                temperature=0.7,  # 较高温度增加创造性
                **openai_config,
            )
        else:
            # 默认模型
            return ChatOpenAI(model_name="gpt-4o", temperature=0.5, **openai_config)

    else:
        raise ValueError(f"不支持的模型提供商: {model_provider}")


def validate_config():
    """验证配置是否完整"""
    # 数据库相关的必需变量
    required_db_vars = ["MYSQL_HOST", "MYSQL_USER", "MYSQL_DATABASE"]
    missing_vars = []

    for var in required_db_vars:
        if not os.getenv(var):
            missing_vars.append(var)

    # 检查至少有一个可用的模型 API 密钥
    has_openai = bool(os.getenv("OPENAI_API_KEY"))
    has_tongyi = bool(os.getenv("DASHSCOPE_API_KEY"))

    if not (has_openai or has_tongyi):
        missing_vars.append("OPENAI_API_KEY 或 DASHSCOPE_API_KEY")

    if missing_vars:
        raise ValueError(f"缺少必要的环境变量: {', '.join(missing_vars)}")

    return True


def get_available_models():
    """获取可用的模型列表"""
    available = []

    if os.getenv("OPENAI_API_KEY"):
        available.append(
            {
                "provider": "openai",
                "models": ["gpt-4o", "gpt-4-turbo", "gpt-3.5-turbo"],
                "description": "OpenAI GPT 系列模型",
            }
        )

    if os.getenv("DASHSCOPE_API_KEY") and TONGYI_AVAILABLE:
        available.append(
            {
                "provider": "tongyi",
                "models": ["qwen-max", "qwen-plus", "qwen-turbo"],
                "description": "阿里云通义千问系列模型",
            }
        )

    return available
