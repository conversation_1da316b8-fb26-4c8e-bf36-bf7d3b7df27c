# 🤖 阿里云通义千问模型集成指南

## 📋 概述

项目现已支持阿里云通义千问系列模型，包括 qwen-max、qwen-plus 和 qwen-turbo。通义千问在中文理解和生成方面表现优异，特别适合中文内容的数据分析任务。

## 🚀 快速配置

### 1. 获取 API 密钥

1. 访问 [阿里云百炼平台](https://bailian.console.aliyun.com/)
2. 注册并登录阿里云账号
3. 开通 DashScope 服务
4. 在控制台获取 API Key

### 2. 安装依赖

```bash
# 安装通义模型相关依赖
pip install dashscope langchain-community

# 或者重新安装所有依赖
pip install -r requirements.txt
```

### 3. 配置环境变量

在 `.env` 文件中添加通义模型的 API 密钥：

```ini
# 阿里云通义系列模型
DASHSCOPE_API_KEY="sk-your-dashscope-api-key"

# 其他配置保持不变
OPENAI_API_KEY="sk-your-openai-api-key"  # 可选
MYSQL_HOST="your-mysql-host"
MYSQL_USER="your-mysql-user"
MYSQL_PASSWORD="your-mysql-password"
MYSQL_DATABASE="your-database"
```

## 🎯 模型选择策略

### 自动模式（推荐）

系统会自动选择可用的最佳模型：

1. 如果配置了 `DASHSCOPE_API_KEY`，优先使用通义模型
2. 如果只配置了 `OPENAI_API_KEY`，使用 OpenAI 模型
3. 如果都配置了，默认使用通义模型

### 手动指定模型

在聊天界面中可以指定使用的模型：

```
# 使用通义模型
通义模式 分析上周粉丝增长最快的达人

# 使用 OpenAI 模型
openai模式 分析上周粉丝增长最快的达人

# 深度分析 + 通义模型
深度模式 通义模式 分析美妆类达人的内容策略
```

## 🔧 模型配置详情

### 角色分配

不同角色使用不同的通义模型：

- **数据分析师**: `qwen-plus`
  - 速度快，精确度高
  - 适合数据查询和统计分析
  - 温度设置：0.1（确保分析准确性）

- **报告撰写人**: `qwen-max`
  - 能力最强，创造性好
  - 适合生成专业报告和洞察
  - 温度设置：0.7（增加创造性）

- **数据验证专家**: `qwen-plus`
  - 逻辑严密，适合验证工作
  - 温度设置：0.1（确保验证准确性）

### 模型特点对比

| 模型 | 特点 | 适用场景 | 成本 |
|------|------|----------|------|
| qwen-max | 能力最强，推理能力优秀 | 复杂分析、报告撰写 | 较高 |
| qwen-plus | 平衡性能和成本 | 数据分析、验证 | 中等 |
| qwen-turbo | 速度快，成本低 | 简单查询、快速响应 | 较低 |

## 💡 使用建议

### 中文内容分析

通义模型在以下场景表现更佳：

- 中文文本理解和生成
- 中国市场相关的业务分析
- 中文社交媒体内容分析
- 本土化的商业洞察

### 最佳实践

1. **内容类型选择**：
   - 中文为主的分析 → 使用通义模式
   - 英文或技术分析 → 使用 OpenAI 模式
   - 混合内容 → 使用自动模式

2. **成本优化**：
   - 简单查询使用 qwen-turbo
   - 重要分析使用 qwen-max
   - 日常分析使用 qwen-plus

3. **性能优化**：
   - 批量分析时考虑使用 qwen-plus
   - 实时交互使用 qwen-turbo
   - 深度报告使用 qwen-max

## 🧪 测试验证

### 1. 测试配置

```bash
# 运行组件测试
python test_components.py
```

### 2. 测试查询

```bash
# 测试通义模型
python -c "
from src.config import get_llm_by_role
llm = get_llm_by_role('data_analyst', 'tongyi')
print('通义模型配置成功')
"
```

### 3. 应用测试

启动应用并测试：

```bash
python run.py
```

在聊天界面输入：`通义模式 测试查询`

## ⚠️ 注意事项

### API 限制

- 确保 DashScope 账户有足够的额度
- 注意 API 调用频率限制
- 监控使用量避免超出配额

### 错误处理

常见错误及解决方案：

1. **API 密钥无效**
   ```
   错误：DASHSCOPE_API_KEY 环境变量未设置
   解决：检查 .env 文件中的 API 密钥
   ```

2. **依赖缺失**
   ```
   错误：通义模型依赖未安装
   解决：pip install dashscope langchain-community
   ```

3. **网络连接问题**
   ```
   错误：连接超时
   解决：检查网络连接，确保可以访问阿里云服务
   ```

### 性能监控

建议监控以下指标：

- API 调用延迟
- 成功率
- 成本消耗
- 响应质量

## 🔄 模型切换

### 运行时切换

无需重启应用，可以在聊天中随时切换：

```
# 从通义切换到 OpenAI
openai模式 继续分析

# 从 OpenAI 切换到通义
通义模式 继续分析
```

### 默认模型修改

修改 `src/config.py` 中的自动选择逻辑：

```python
# 优先使用 OpenAI
if os.getenv("OPENAI_API_KEY"):
    model_provider = "openai"
elif os.getenv("DASHSCOPE_API_KEY") and TONGYI_AVAILABLE:
    model_provider = "tongyi"
```

## 📊 性能对比

基于实际测试的性能对比：

| 指标 | 通义千问 | OpenAI GPT |
|------|----------|------------|
| 中文理解 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| 英文理解 | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| 逻辑推理 | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| 响应速度 | ⭐⭐⭐⭐ | ⭐⭐⭐ |
| 成本效益 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |

现在您可以充分利用阿里云通义千问模型的强大能力来分析 TikTok 数据了！
