# 🧠 Vanna 训练指南

## 📋 概述

Vanna 训练逻辑已经抽离为独立模块，您可以通过 JSON 配置文件来训练 Vanna 模型，让它学习您的数据库结构和查询模式。

## 🚀 快速开始

### 1. 生成示例配置

```bash
# 生成默认配置文件
python train_vanna.py --sample

# 生成自定义名称的配置文件
python train_vanna.py --sample --output my_training.json
```

### 2. 编辑配置文件

编辑生成的 `training_config.json` 文件，添加您的数据库结构和示例查询。

### 3. 开始训练

```bash
# 从配置文件训练
python train_vanna.py --config training_config.json

# 交互式训练
python train_vanna.py
```

### 4. 测试查询

```bash
# 测试单个查询
python train_vanna.py --test "查询粉丝数最多的达人"

# 查看训练信息
python train_vanna.py --info
```

## 📝 配置文件格式

训练配置文件是一个 JSON 文件，包含以下部分：

### DDL 定义 (ddl)

定义数据库表结构，让 Vanna 了解您的数据模型：

```json
{
  "ddl": [
    {
      "sql": "CREATE TABLE creators (...);",
      "description": "创作者表结构"
    }
  ]
}
```

### SQL 示例 (sql_examples)

提供问题和对应的 SQL 查询示例：

```json
{
  "sql_examples": [
    {
      "question": "查询粉丝数最多的10个达人",
      "sql": "SELECT username, followers_count FROM creators ORDER BY followers_count DESC LIMIT 10;",
      "description": "热门达人查询"
    }
  ]
}
```

### 文档说明 (documentation)

添加业务逻辑和表字段的详细说明：

```json
{
  "documentation": [
    {
      "content": "creators 表存储 TikTok 达人的基本信息...",
      "description": "creators 表说明"
    }
  ]
}
```

### 查询计划 (plans)

定义复杂分析的步骤计划：

```json
{
  "plans": [
    {
      "question": "分析达人的内容策略",
      "plan": "1. 查询达人基本信息\n2. 分析视频播放量\n3. 统计话题标签...",
      "description": "达人内容策略分析计划"
    }
  ]
}
```

## 🛠️ 命令行工具

### 基本命令

```bash
# 显示帮助
python train_vanna.py --help

# 生成示例配置
python train_vanna.py --sample

# 从配置文件训练
python train_vanna.py --config your_config.json

# 测试查询
python train_vanna.py --test "您的问题"

# 显示训练信息
python train_vanna.py --info

# 交互式模式
python train_vanna.py
```

### 参数说明

- `--config, -c`: 指定训练配置文件路径
- `--sample, -s`: 生成示例配置文件
- `--test, -t`: 测试查询功能
- `--info, -i`: 显示训练信息
- `--output, -o`: 指定示例配置输出文件名

## 📊 训练最佳实践

### 1. DDL 训练

- 包含所有重要的表结构
- 添加外键关系定义
- 包含索引和约束信息

### 2. SQL 示例训练

- 覆盖常见的查询模式
- 包含简单和复杂查询
- 添加聚合、连接、子查询示例
- 包含时间范围查询

### 3. 文档训练

- 解释业务术语和字段含义
- 说明计算公式和业务规则
- 提供数据范围和约束说明

### 4. 计划训练

- 定义复杂分析的步骤
- 包含多表关联的分析流程
- 提供业务分析的思路模板

## 🔧 集成到应用

训练完成后，您的 Chainlit 应用会自动使用训练好的模型：

1. **自动加载**: `DatabaseQueryTool` 会自动连接到训练好的 Vanna 模型
2. **智能查询**: 用户的自然语言问题会被转换为准确的 SQL 查询
3. **上下文理解**: Vanna 会根据训练数据理解业务术语和查询意图

## 🚨 注意事项

### 训练前准备

1. **确保数据库连接正常**
   ```bash
   # 测试数据库连接
   python -c "from src.tools.database_tools import DatabaseConnectionTool; print(DatabaseConnectionTool()._run(''))"
   ```

2. **检查环境配置**
   ```bash
   # 验证配置
   python -c "from src.config import validate_config; validate_config()"
   ```

### 训练质量

- **逐步训练**: 先训练基础结构，再添加复杂示例
- **测试验证**: 每次训练后测试几个查询确保效果
- **迭代改进**: 根据实际使用效果调整训练数据

### 性能优化

- **合理数量**: 不要一次训练过多数据
- **质量优先**: 高质量的示例比数量更重要
- **定期更新**: 根据新的业务需求更新训练数据

## 📈 监控和维护

### 查看训练状态

```bash
# 查看当前训练信息
python train_vanna.py --info
```

### 测试查询效果

```bash
# 测试常见查询
python train_vanna.py --test "查询粉丝数最多的达人"
python train_vanna.py --test "分析上周的视频数据"
```

### 持续改进

1. 收集用户常问的问题
2. 分析查询失败的案例
3. 更新训练配置文件
4. 重新训练模型

## 🎯 示例工作流

```bash
# 1. 生成配置模板
python train_vanna.py --sample --output my_tiktok_training.json

# 2. 编辑配置文件（添加您的表结构和示例）
# 编辑 my_tiktok_training.json

# 3. 开始训练
python train_vanna.py --config my_tiktok_training.json

# 4. 测试效果
python train_vanna.py --test "查询最受欢迎的达人"

# 5. 启动应用
python run.py
```

现在您可以通过简单的 JSON 配置来训练 Vanna，让它更好地理解您的 TikTok 数据库！
