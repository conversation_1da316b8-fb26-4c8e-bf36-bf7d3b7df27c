# 📁 TikTok 数据分析系统 - 项目结构

## 🎯 整理后的目录结构

```
rps/
├── 📄 app.py                    # Chainlit 主应用
├── 📄 run.py                    # 应用启动脚本
├── 📄 README.md                 # 项目说明
├── 📄 requirements.txt          # Python 依赖
├── 📄 pyproject.toml           # 项目配置
├── 📄 chainlit.md              # Chainlit 界面配置
├── 📄 migrate_data.py          # 数据迁移工具
├── 📄 update_paths_config.py   # 路径配置更新工具
│
├── 📁 src/                     # 源代码目录
│   ├── 📄 __init__.py
│   ├── 📄 config.py            # 配置管理
│   ├── 📄 agents.py            # CrewAI 智能体定义
│   ├── 📄 tasks.py             # CrewAI 任务定义
│   ├── 📄 crew.py              # CrewAI 团队配置
│   ├── 📄 vanna_trainer.py     # Vanna 训练器
│   ├── 📄 data_manager.py      # 数据管理工具
│   ├── 📄 database_manager.py  # 数据库管理
│   ├── 📄 vanna_paths.py       # Vanna 路径配置
│   ├── 📁 config/              # 配置文件目录
│   └── 📁 tools/               # 工具模块
│       ├── 📄 __init__.py
│       ├── 📄 database_tools.py      # 数据库查询工具
│       ├── 📄 data_formatter.py      # 数据格式化器
│       └── 📄 data_enhancer.py       # 数据增强器
│
├── 📁 data/                    # 数据存储目录 ⭐ 新增
│   ├── 📄 README.md            # 数据目录说明
│   ├── 📄 .gitignore           # Git 忽略配置
│   ├── 📁 cache/               # 缓存数据
│   │   ├── 📁 chromadb/        # ChromaDB 向量数据库缓存
│   │   │   ├── 📄 chroma.sqlite3           # 主数据库文件
│   │   │   ├── 📄 chromadb-*.lock          # 锁文件
│   │   │   └── 📁 [UUID目录]/              # 向量数据目录
│   │   └── 📁 python/          # Python 运行时缓存
│   │       └── 📁 __pycache__/
│   ├── 📁 training/            # 训练数据
│   │   ├── 📄 training_config.json        # 训练配置
│   │   ├── 📄 train_vanna.py              # Vanna 训练脚本
│   │   ├── 📄 quick_train_vanna.py        # 快速训练脚本
│   │   ├── 📁 vanna/           # Vanna 训练数据
│   │   └── 📁 models/          # 训练好的模型
│   ├── 📁 logs/                # 日志文件
│   ├── 📁 temp/                # 临时文件
│   └── 📁 exports/             # 导出数据
│
├── 📁 tests/                   # 测试文件目录 ⭐ 新增
│   ├── 📄 test_*.py            # 各种测试脚本
│   ├── 📄 debug_*.py           # 调试脚本
│   ├── 📄 fix_*.py             # 修复脚本
│   ├── 📄 check_*.py           # 检查脚本
│   └── 📄 setup_env.py         # 环境设置脚本
│
└── 📁 docs/                    # 文档目录 ⭐ 新增
    ├── 📄 QUICK_START.md       # 快速开始指南
    ├── 📄 *_GUIDE.md           # 各种使用指南
    ├── 📄 *_SUMMARY.md         # 功能总结文档
    └── 📄 *_README.md          # 详细说明文档
```

## 🔄 主要变化

### ✅ 新增目录

1. **📁 data/** - 统一的数据存储目录
   - `cache/chromadb/` - ChromaDB 缓存文件
   - `training/` - 训练数据和脚本
   - `logs/` - 日志文件
   - `temp/` - 临时文件
   - `exports/` - 导出数据

2. **📁 tests/** - 测试文件目录
   - 所有测试、调试、修复脚本

3. **📁 docs/** - 文档目录
   - 所有说明文档和指南

### 🗑️ 清理内容

- ❌ 根目录下的 ChromaDB 文件 (`chroma.sqlite3`, `*.lock`, UUID目录)
- ❌ 根目录下的 Python 缓存 (`__pycache__/`)
- ❌ 根目录下的训练文件 (`training_config.json`, `train_vanna.py`)
- ❌ 根目录下的测试文件 (`test_*.py`, `debug_*.py`, `fix_*.py`)
- ❌ 根目录下的文档文件 (`*_GUIDE.md`, `*_SUMMARY.md`)

## 🔧 配置更新

### 1. 路径配置
- 创建了 `src/vanna_paths.py` 来管理 Vanna 相关路径
- 更新了 `src/tools/database_tools.py` 以使用新路径
- 环境变量自动设置正确的数据库路径

### 2. 数据管理
- 创建了 `src/data_manager.py` 数据管理工具
- 提供缓存清理、备份、存储信息查看功能

### 3. 迁移工具
- `migrate_data.py` - 用于迁移旧数据到新结构
- `update_paths_config.py` - 更新路径配置

## 💡 使用方法

### 数据管理
```python
from src.data_manager import DataManager

manager = DataManager()

# 查看存储信息
manager.get_storage_info()

# 清理缓存
manager.clean_cache()

# 备份训练数据
manager.backup_training_data()
```

### 路径获取
```python
from src.vanna_paths import get_chromadb_path, get_training_config_path

# 获取 ChromaDB 路径
db_path = get_chromadb_path()

# 获取训练配置路径
config_path = get_training_config_path()
```

## 🎯 优势

### 1. 📁 结构清晰
- 根目录整洁，只保留核心文件
- 数据、测试、文档分类存储
- 易于维护和管理

### 2. 🔒 版本控制友好
- `data/.gitignore` 自动忽略缓存文件
- 重要配置文件保留在版本控制中
- 避免提交大型缓存文件

### 3. 🛠️ 易于管理
- 统一的数据管理工具
- 自动路径配置
- 完善的备份和恢复机制

### 4. 🚀 部署友好
- 清晰的目录结构便于部署
- 数据和代码分离
- 易于配置不同环境

## 🔄 迁移步骤

如果您有旧的项目结构，可以使用以下步骤迁移：

1. **运行迁移脚本**：
   ```bash
   python migrate_data.py
   ```

2. **更新路径配置**：
   ```bash
   python update_paths_config.py
   ```

3. **测试功能**：
   ```bash
   python -c "from src.data_manager import DataManager; DataManager().get_storage_info()"
   ```

4. **启动应用**：
   ```bash
   chainlit run app.py
   ```

## 📝 注意事项

1. **不要手动删除** `data/cache/chromadb/` 中的文件，这会导致需要重新训练
2. **定期备份** `data/training/` 目录中的重要数据
3. **定期清理** `data/temp/` 和 `data/logs/` 中的旧文件
4. **版本控制**: `data/` 目录中的大部分文件不会提交到 Git

## 🎉 总结

通过这次整理，项目结构更加清晰和专业：
- ✅ 根目录整洁
- ✅ 数据统一管理
- ✅ 测试文件分类
- ✅ 文档集中存放
- ✅ 配置自动化
- ✅ 版本控制友好

现在您的 TikTok 数据分析系统具有了更好的可维护性和扩展性！🚀
