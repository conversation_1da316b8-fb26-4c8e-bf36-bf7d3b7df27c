"""
简单的 Vanna 权限测试脚本
"""

import os
import sys

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试导入"""
    print("🧪 测试导入...")
    
    try:
        from src.config import VANNA_CONFIG, VANNA_DB_CONFIG
        print("✅ 配置导入成功")
        
        print(f"📋 VANNA_CONFIG keys: {list(VANNA_CONFIG.keys())}")
        print(f"📋 VANNA_DB_CONFIG keys: {list(VANNA_DB_CONFIG.keys())}")
        
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False


def test_database_tool():
    """测试数据库工具"""
    print("\n💾 测试数据库工具...")
    
    try:
        from src.tools.database_tools import DatabaseQueryTool
        
        # 创建工具实例
        tool = DatabaseQueryTool()
        print("✅ DatabaseQueryTool 创建成功")
        
        # 测试简单查询
        test_question = "查询数据库中有多少个表"
        print(f"🔍 测试查询: {test_question}")
        
        result = tool._run(test_question)
        print("📊 查询结果:")
        print(result[:500] + "..." if len(result) > 500 else result)
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库工具测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def check_environment():
    """检查环境变量"""
    print("\n🔍 检查环境变量...")
    
    env_vars = [
        "OPENAI_API_KEY",
        "OPENAI_BASE_URL", 
        "MYSQL_HOST",
        "MYSQL_USER",
        "MYSQL_PASSWORD",
        "MYSQL_DATABASE",
        "VANNA_API_KEY",
        "VANNA_MODEL"
    ]
    
    for var in env_vars:
        value = os.getenv(var)
        if value:
            if 'key' in var.lower() or 'password' in var.lower():
                print(f"  ✅ {var}: {'*' * min(len(value), 10)}")
            else:
                print(f"  ✅ {var}: {value}")
        else:
            print(f"  ❌ {var}: 未设置")


def main():
    """主函数"""
    print("🔧 Vanna 简单测试")
    print("="*30)
    
    # 检查环境变量
    check_environment()
    
    # 测试导入
    if not test_imports():
        return
    
    # 测试数据库工具
    test_database_tool()


if __name__ == "__main__":
    main()
