"""
数据库连接管理器 - 处理连接超时和连接池
"""

import mysql.connector
from mysql.connector import pooling
import logging
from typing import Optional, Dict, Any
from .config import DATABASE_CONFIG

logger = logging.getLogger(__name__)


class DatabaseManager:
    """数据库连接管理器"""
    
    _connection_pool: Optional[pooling.MySQLConnectionPool] = None
    
    @classmethod
    def get_connection_pool(cls) -> pooling.MySQLConnectionPool:
        """获取连接池（单例模式）"""
        if cls._connection_pool is None:
            try:
                # 创建连接池配置
                pool_config = {
                    "pool_name": DATABASE_CONFIG.get("pool_name", "vanna_pool"),
                    "pool_size": DATABASE_CONFIG.get("pool_size", 5),
                    "pool_reset_session": DATABASE_CONFIG.get("pool_reset_session", True),
                    "host": DATABASE_CONFIG["host"],
                    "user": DATABASE_CONFIG["user"],
                    "password": DATABASE_CONFIG["password"],
                    "database": DATABASE_CONFIG["database"],
                    "port": DATABASE_CONFIG["port"],
                    "charset": DATABASE_CONFIG.get("charset", "utf8mb4"),
                    "use_unicode": DATABASE_CONFIG.get("use_unicode", True),
                    "autocommit": DATABASE_CONFIG.get("autocommit", True),
                    "connection_timeout": DATABASE_CONFIG.get("connection_timeout", 60),
                    # SQL 执行超时设置
                    "sql_mode": "TRADITIONAL",
                    # 连接选项
                    "connect_timeout": 10,  # 连接建立超时
                    "read_timeout": 30,     # 读取超时
                    "write_timeout": 30,    # 写入超时
                }
                
                cls._connection_pool = pooling.MySQLConnectionPool(**pool_config)
                logger.info(f"数据库连接池创建成功: {pool_config['pool_name']} (大小: {pool_config['pool_size']})")
                
            except Exception as e:
                logger.error(f"创建数据库连接池失败: {e}")
                raise
        
        return cls._connection_pool
    
    @classmethod
    def get_connection(cls):
        """从连接池获取连接"""
        try:
            pool = cls.get_connection_pool()
            connection = pool.get_connection()
            
            # 设置会话级别的超时
            cursor = connection.cursor()
            cursor.execute("SET SESSION wait_timeout = 300")  # 5分钟
            cursor.execute("SET SESSION interactive_timeout = 300")  # 5分钟
            cursor.execute("SET SESSION net_read_timeout = 60")  # 1分钟
            cursor.execute("SET SESSION net_write_timeout = 60")  # 1分钟
            cursor.close()
            
            return connection
            
        except Exception as e:
            logger.error(f"获取数据库连接失败: {e}")
            raise
    
    @classmethod
    def execute_query(cls, sql: str, params: Optional[tuple] = None) -> Any:
        """
        执行查询并自动管理连接
        
        Args:
            sql (str): SQL 语句
            params (Optional[tuple]): 查询参数
            
        Returns:
            查询结果
        """
        connection = None
        cursor = None
        
        try:
            connection = cls.get_connection()
            cursor = connection.cursor(dictionary=True)
            
            # 执行查询
            if params:
                cursor.execute(sql, params)
            else:
                cursor.execute(sql)
            
            # 获取结果
            if sql.strip().upper().startswith('SELECT'):
                result = cursor.fetchall()
            else:
                result = cursor.rowcount
                connection.commit()
            
            return result
            
        except Exception as e:
            logger.error(f"执行查询失败: {e}")
            if connection:
                connection.rollback()
            raise
            
        finally:
            if cursor:
                cursor.close()
            if connection:
                connection.close()  # 返回到连接池
    
    @classmethod
    def test_connection(cls) -> Dict[str, Any]:
        """测试数据库连接"""
        try:
            connection = cls.get_connection()
            cursor = connection.cursor()
            
            # 测试基本连接
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            
            # 获取超时设置
            cursor.execute("SELECT @@wait_timeout, @@interactive_timeout, @@net_read_timeout, @@net_write_timeout")
            timeouts = cursor.fetchone()
            
            # 获取连接信息
            cursor.execute("SELECT CONNECTION_ID()")
            connection_id = cursor.fetchone()
            
            cursor.close()
            connection.close()
            
            return {
                "success": True,
                "version": version[0],
                "connection_id": connection_id[0],
                "timeouts": {
                    "wait_timeout": timeouts[0],
                    "interactive_timeout": timeouts[1],
                    "net_read_timeout": timeouts[2],
                    "net_write_timeout": timeouts[3]
                },
                "pool_info": {
                    "pool_name": DATABASE_CONFIG.get("pool_name"),
                    "pool_size": DATABASE_CONFIG.get("pool_size"),
                    "connection_timeout": DATABASE_CONFIG.get("connection_timeout")
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e)
            }
    
    @classmethod
    def close_pool(cls):
        """关闭连接池"""
        if cls._connection_pool:
            try:
                # 注意：mysql.connector 的连接池没有直接的 close 方法
                # 我们只需要将引用设为 None，让垃圾回收器处理
                cls._connection_pool = None
                logger.info("数据库连接池已关闭")
            except Exception as e:
                logger.error(f"关闭连接池失败: {e}")


# 便捷函数
def get_db_connection():
    """获取数据库连接的便捷函数"""
    return DatabaseManager.get_connection()


def execute_sql(sql: str, params: Optional[tuple] = None):
    """执行 SQL 的便捷函数"""
    return DatabaseManager.execute_query(sql, params)


def test_db_connection():
    """测试数据库连接的便捷函数"""
    return DatabaseManager.test_connection()
