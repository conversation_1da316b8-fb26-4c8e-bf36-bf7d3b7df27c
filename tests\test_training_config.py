"""
测试训练配置文件
"""
import json
import sys

def test_training_config():
    """测试训练配置文件格式"""
    try:
        with open('training_config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print("✅ 配置文件格式正确")
        
        # 检查必要的部分
        required_sections = ['ddl', 'sql_examples', 'documentation', 'plans']
        for section in required_sections:
            if section in config:
                print(f"✅ {section} 部分存在，包含 {len(config[section])} 项")
            else:
                print(f"❌ 缺少 {section} 部分")
        
        # 统计信息
        print(f"\n📊 配置统计:")
        print(f"  - DDL 定义: {len(config.get('ddl', []))} 个表")
        print(f"  - SQL 示例: {len(config.get('sql_examples', []))} 个")
        print(f"  - 文档说明: {len(config.get('documentation', []))} 个")
        print(f"  - 分析计划: {len(config.get('plans', []))} 个")
        
        # 显示表名
        if 'ddl' in config:
            print(f"\n📋 数据库表:")
            for ddl in config['ddl']:
                if 'CREATE TABLE' in ddl['sql']:
                    table_name = ddl['sql'].split('`')[1] if '`' in ddl['sql'] else "未知表名"
                    print(f"  - {table_name}: {ddl['description']}")
        
        # 显示示例查询类型
        if 'sql_examples' in config:
            print(f"\n🔍 查询示例类型:")
            for example in config['sql_examples'][:5]:  # 只显示前5个
                print(f"  - {example['description']}")
            if len(config['sql_examples']) > 5:
                print(f"  - ... 还有 {len(config['sql_examples']) - 5} 个示例")
        
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ JSON 格式错误: {e}")
        return False
    except FileNotFoundError:
        print("❌ 找不到 training_config.json 文件")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

if __name__ == "__main__":
    print("🧪 测试 TikTok 数据分析训练配置")
    print("=" * 50)
    
    success = test_training_config()
    
    if success:
        print("\n🎉 配置文件测试通过！")
        print("💡 您可以使用以下命令开始训练:")
        print("   python train_vanna.py --config training_config.json")
    else:
        print("\n❌ 配置文件测试失败，请检查文件格式")
        sys.exit(1)
