"""
测试修复后的DatabaseQueryTool
"""

import sys
import os
import pandas as pd

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_data_formatter_import():
    """测试数据格式化器导入"""
    print("🧪 测试数据格式化器导入...")
    
    try:
        from src.tools.data_formatter import DataFormatter
        formatter = DataFormatter()
        print("✅ DataFormatter 导入和初始化成功")
        
        # 测试字段映射
        mappings = formatter.field_mappings
        print(f"📊 加载了 {len(mappings)} 个表的字段映射")
        
        return True
    except Exception as e:
        print(f"❌ DataFormatter 导入失败: {e}")
        return False


def test_data_enhancer_import():
    """测试数据增强器导入"""
    print("\n🚀 测试数据增强器导入...")
    
    try:
        from src.tools.data_enhancer import DataEnhancer
        enhancer = DataEnhancer()
        print("✅ DataEnhancer 导入和初始化成功")
        
        # 测试查询意图分析
        intent, keywords = enhancer.analyze_query_intent("查询粉丝数最多的达人")
        print(f"🔍 查询意图分析测试: {intent}")
        
        return True
    except Exception as e:
        print(f"❌ DataEnhancer 导入失败: {e}")
        return False


def test_database_tool_init():
    """测试DatabaseQueryTool初始化"""
    print("\n💾 测试DatabaseQueryTool初始化...")
    
    try:
        # 模拟BaseTool类
        class MockBaseTool:
            def __init__(self, **data):
                pass
        
        # 临时替换BaseTool
        import src.tools.database_tools as db_tools
        original_base_tool = db_tools.BaseTool
        db_tools.BaseTool = MockBaseTool
        
        # 设置全局变量避免Vanna初始化
        db_tools._global_vanna_available = True
        db_tools._global_vn_instance = "mock_instance"
        
        try:
            from src.tools.database_tools import DatabaseQueryTool
            
            # 创建工具实例
            tool = DatabaseQueryTool()
            
            # 检查属性
            if hasattr(tool, 'data_formatter'):
                print("✅ data_formatter 属性存在")
                if tool.data_formatter is not None:
                    print("✅ data_formatter 初始化成功")
                else:
                    print("⚠️ data_formatter 为 None（可能是配置文件问题）")
            else:
                print("❌ data_formatter 属性不存在")
            
            if hasattr(tool, 'data_enhancer'):
                print("✅ data_enhancer 属性存在")
                if tool.data_enhancer is not None:
                    print("✅ data_enhancer 初始化成功")
                else:
                    print("⚠️ data_enhancer 为 None（可能是配置文件问题）")
            else:
                print("❌ data_enhancer 属性不存在")
            
            return True
            
        finally:
            # 恢复原始BaseTool
            db_tools.BaseTool = original_base_tool
            
    except Exception as e:
        print(f"❌ DatabaseQueryTool 初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_fallback_formatting():
    """测试回退格式化功能"""
    print("\n📊 测试回退格式化功能...")
    
    try:
        # 创建测试数据
        test_data = {
            'unique_id': ['user1', 'user2'],
            'author_name': ['达人A', '达人B'],
            'follower_count': [1234567, 987654]
        }
        df = pd.DataFrame(test_data)
        
        # 模拟DatabaseQueryTool的回退格式化方法
        def format_dataframe_fallback(df, sql):
            max_rows = 20
            if len(df) > max_rows:
                df_display = df.head(max_rows)
                truncated_note = f"\n\n*注：仅显示前 {max_rows} 行，总共 {len(df)} 行数据*"
            else:
                df_display = df
                truncated_note = f"\n\n*总共 {len(df)} 行数据*"

            markdown_table = df_display.to_markdown(index=False, tablefmt="pipe")
            return f"## 查询结果\n\n**执行的 SQL:**\n```sql\n{sql}\n```\n\n**查询结果:**\n{markdown_table}{truncated_note}"
        
        # 测试格式化
        sql = "SELECT unique_id, author_name, follower_count FROM at_tiktok_author_pool LIMIT 2"
        result = format_dataframe_fallback(df, sql)
        
        print("✅ 回退格式化功能正常")
        print("📋 格式化结果预览:")
        print(result[:200] + "..." if len(result) > 200 else result)
        
        return True
        
    except Exception as e:
        print(f"❌ 回退格式化测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🔧 DatabaseQueryTool 修复验证测试")
    print("="*50)
    
    # 测试各个组件
    formatter_ok = test_data_formatter_import()
    enhancer_ok = test_data_enhancer_import()
    tool_ok = test_database_tool_init()
    fallback_ok = test_fallback_formatting()
    
    print("\n" + "="*50)
    print("📊 测试结果汇总:")
    print(f"  DataFormatter: {'✅ 通过' if formatter_ok else '❌ 失败'}")
    print(f"  DataEnhancer: {'✅ 通过' if enhancer_ok else '❌ 失败'}")
    print(f"  DatabaseQueryTool: {'✅ 通过' if tool_ok else '❌ 失败'}")
    print(f"  回退格式化: {'✅ 通过' if fallback_ok else '❌ 失败'}")
    
    if all([formatter_ok, enhancer_ok, tool_ok, fallback_ok]):
        print("\n🎉 所有测试通过！DatabaseQueryTool 修复成功")
        print("\n💡 现在可以安全使用增强的查询功能了")
    else:
        print("\n⚠️ 部分测试失败，但基本功能应该可用")
        print("💡 即使增强功能不可用，也会回退到基本格式化")


if __name__ == "__main__":
    main()
