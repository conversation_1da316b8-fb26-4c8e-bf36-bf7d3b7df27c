# 🚀 TikTok 数据分析增强功能使用指南

## 📋 概述

本指南介绍了新增的数据格式化和字段映射功能，这些功能让数据返回更加用户友好和结构化。

## ✨ 主要特性

### 1. 🔄 智能字段映射
- **自动转换**: 数据库原始字段自动转换为中文说明
- **用户友好**: 返回的数据使用易懂的中文字段名
- **完整映射**: 覆盖达人信息表和作品信息表的所有字段

### 2. 🧠 查询意图识别
- **智能分析**: 自动识别用户查询的意图类型
- **自动增强**: 根据意图补充相关的基本信息字段
- **模式匹配**: 支持多种查询模式的识别

### 3. 📊 结构化数据返回
- **统一格式**: 所有查询结果采用统一的结构化格式
- **汇总统计**: 自动计算并显示数据汇总信息
- **字段说明**: 提供详细的字段说明和描述

### 4. 🔢 衍生指标计算
- **互动率**: 自动计算 (点赞+评论+分享)/播放量 * 100%
- **点赞率**: 自动计算 点赞数/播放量 * 100%
- **数值格式化**: 大数字自动添加千分位分隔符

## 🎯 使用示例

### 达人查询示例

**用户输入**: "查询粉丝数最多的达人"

**系统行为**:
1. 识别查询意图: "达人排行"
2. 自动补充字段: 用户名、达人昵称、粉丝数、总获赞数、发布视频数、注册地区、认证状态
3. 字段名转换: `follower_count` → `粉丝数`, `author_name` → `达人昵称`
4. 数值格式化: `1234567` → `1,234,567`

**返回结果格式**:
```markdown
## 📊 查询结果

**查询问题:** 查询粉丝数最多的达人
**查询意图:** 达人排行
**数据总数:** 10 条

**执行的 SQL:**
```sql
SELECT unique_id, author_name, follower_count, heart_count, video_count, region, is_verified 
FROM at_tiktok_author_pool 
WHERE is_del = 0 
ORDER BY follower_count DESC 
LIMIT 10;
```

**查询结果:**
| 用户名 | 达人昵称 | 粉丝数 | 总获赞数 | 发布视频数 | 注册地区 | 认证状态 |
|--------|----------|--------|----------|------------|----------|----------|
| user1  | 达人A    | 1,234,567 | 5,678,901 | 234 | US | 已认证 |
| user2  | 达人B    | 987,654   | 3,456,789 | 156 | CN | 未认证 |

## 📈 数据汇总
- **粉丝数_总计**: 12,345,678
- **粉丝数_平均**: 1,234,568
- **总获赞数_总计**: 45,678,901
- **总获赞数_平均**: 4,567,890

## 📝 字段说明
- **用户名**: 用户名 (原字段: unique_id)
- **达人昵称**: 达人昵称 (原字段: author_name)
- **粉丝数**: 粉丝数 (原字段: follower_count)
```

### 作品查询示例

**用户输入**: "分析播放量超过100万的视频"

**系统行为**:
1. 识别查询意图: "作品排行"
2. 自动补充字段: 作品标题、用户名、达人昵称、播放量、点赞数、评论数、分享数、发布时间
3. 计算衍生指标: 互动率、点赞率
4. 格式化数值和时间

## 🔧 配置说明

### 字段映射配置 (`src/config/field_mappings.json`)

```json
{
  "field_mappings": {
    "at_tiktok_author_pool": {
      "author_id": "达人ID",
      "unique_id": "用户名",
      "author_name": "达人昵称",
      "follower_count": "粉丝数"
    }
  },
  "data_enhancers": {
    "达人基本信息": {
      "required_fields": ["unique_id", "author_name", "follower_count"],
      "optional_fields": ["region", "is_verified"]
    }
  }
}
```

### 查询意图类型

| 意图类型 | 触发关键词 | 自动补充字段 | 计算指标 |
|----------|------------|--------------|----------|
| 达人排行 | 粉丝最多、达人排行、热门达人 | 用户名、昵称、粉丝数、获赞数 | - |
| 作品排行 | 播放最多、热门作品、爆款视频 | 标题、作者、播放量、互动数据 | - |
| 互动分析 | 互动率、参与度、点赞率 | 播放量、点赞数、评论数、分享数 | 互动率、点赞率 |
| 电商分析 | 电商、带货、商品 | 电商功能、小店卖家、商品ID | - |
| 地区分析 | 地区、区域、位置 | 注册地区、界面语言、发布位置 | - |

## 🛠️ 开发者指南

### 扩展字段映射

1. 编辑 `src/config/field_mappings.json`
2. 在 `field_mappings` 中添加新表的字段映射
3. 在 `data_enhancers` 中定义增强规则

### 添加新的查询意图

1. 在 `src/tools/data_enhancer.py` 中的 `_build_query_patterns()` 方法添加新模式
2. 在配置文件中添加对应的增强规则
3. 更新智能体的 backstory 说明

### 自定义数据格式化

1. 继承 `DataFormatter` 类
2. 重写 `_format_data_values()` 方法
3. 添加自定义的格式化逻辑

## 🧪 测试

运行测试脚本验证功能：

```bash
python test_enhanced_query.py
```

测试内容包括：
- 配置文件加载
- 字段映射功能
- 查询意图识别
- 数据库查询增强

## 📈 效果对比

### 原始返回格式
```
| unique_id | author_name | follower_count |
|-----------|-------------|----------------|
| user1     | 达人A       | 1234567        |
```

### 增强后返回格式
```
## 📊 查询结果
**查询意图:** 达人排行
**数据总数:** 1 条

| 用户名 | 达人昵称 | 粉丝数 | 总获赞数 | 认证状态 |
|--------|----------|--------|----------|----------|
| user1  | 达人A    | 1,234,567 | 5,678,901 | 已认证 |

## 📈 数据汇总
- **粉丝数_平均**: 1,234,567
- **总获赞数_总计**: 5,678,901

## 📝 字段说明
- **用户名**: 用户名 (原字段: unique_id)
- **达人昵称**: 达人昵称 (原字段: author_name)
```

## 🎉 总结

新的增强功能实现了您的需求：

1. ✅ **结构化友好**: 返回完整的结构化数据，而不是简单的查询结果
2. ✅ **完整信息**: 自动补充相关的基本信息字段
3. ✅ **中文字段**: 所有字段都使用中文说明，而不是原始数据库字段名
4. ✅ **智能增强**: 根据查询意图自动补充相关字段和计算指标

现在当用户查询"点赞数最多的达人"时，系统会自动返回达人的完整基本信息，包括用户名、昵称、粉丝数、获赞数等，所有字段都是中文说明，数据格式友好易读！
