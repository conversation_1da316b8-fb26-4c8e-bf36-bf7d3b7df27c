"""
一致的 Vanna 配置
确保使用统一的嵌入模型
"""

import os
from pathlib import Path


def create_consistent_vanna():
    """创建使用一致嵌入的 Vanna 实例"""
    from vanna.openai import OpenAI_Chat
    from vanna.chromadb import ChromaDB_VectorStore
    
    class ConsistentVanna(ChromaDB_VectorStore, OpenAI_Chat):
        def __init__(self, config=None):
            # 强制使用新的 ChromaDB 路径
            chroma_config = {
                "path": str(Path("data/cache/chromadb").absolute()),
                "collection_name": "vanna_embeddings_v2"  # 使用新的集合名
            }
            ChromaDB_VectorStore.__init__(self, config=chroma_config)
            OpenAI_Chat.__init__(self, config=config)
    
    # 检查 OpenAI API Key
    openai_api_key = os.getenv("OPENAI_API_KEY")
    if not openai_api_key:
        raise ValueError("缺少 OPENAI_API_KEY 环境变量")
    
    config = {
        "api_key": openai_api_key,
        "model": "gpt-3.5-turbo",
        "base_url": os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1")
    }
    
    return ConsistentVanna(config=config)


def setup_database_connection(vn):
    """设置数据库连接"""
    from src.config import VANNA_DB_CONFIG
    
    vn.connect_to_mysql(**VANNA_DB_CONFIG)
    vn.allow_llm_to_see_data = True
    
    return vn
