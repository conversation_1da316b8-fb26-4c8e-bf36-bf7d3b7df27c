"""
Vanna 训练启动脚本
支持从 JSON 文件或命令行参数进行训练
"""

import argparse
import json
import sys
import logging
from pathlib import Path
from src.vanna_trainer import VannaTrainer, create_sample_training_config

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler(sys.stdout)],
)
logger = logging.getLogger(__name__)


def main():
    logger.info("启动 Vanna 训练工具")

    parser = argparse.ArgumentParser(description="Vanna 训练工具")
    parser.add_argument("--config", "-c", type=str, help="训练配置 JSON 文件路径")
    parser.add_argument("--sample", "-s", action="store_true", help="生成示例配置文件")
    parser.add_argument("--test", "-t", type=str, help="测试查询")
    parser.add_argument("--info", "-i", action="store_true", help="显示训练信息")
    parser.add_argument(
        "--output",
        "-o",
        type=str,
        default="training_config.json",
        help="示例配置输出文件名",
    )
    parser.add_argument("--verbose", "-v", action="store_true", help="启用详细日志")

    args = parser.parse_args()

    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
        logger.info("启用详细日志模式")

    logger.debug(f"命令行参数: {args}")

    # 生成示例配置文件
    if args.sample:
        logger.info("执行生成示例配置文件操作")
        generate_sample_config(args.output)
        return

    # 创建训练器
    logger.info("创建 Vanna 训练器实例")
    try:
        trainer = VannaTrainer()
        logger.info("训练器创建成功")
    except Exception as e:
        logger.error(f"训练器创建失败: {e}", exc_info=True)
        print(f"❌ 训练器创建失败: {e}")
        return

    # 显示训练信息
    if args.info:
        logger.info("执行显示训练信息操作")
        show_training_info(trainer)
        return

    # 测试查询
    if args.test:
        logger.info(f"执行测试查询操作: {args.test}")
        test_query(trainer, args.test)
        return

    # 从配置文件训练
    if args.config:
        logger.info(f"执行配置文件训练操作: {args.config}")
        train_from_config(trainer, args.config)
        return

    # 交互式训练
    logger.info("启动交互式训练模式")
    interactive_training(trainer)


def generate_sample_config(output_file: str):
    """生成示例配置文件"""
    logger.info(f"开始生成示例配置文件: {output_file}")
    try:
        config = create_sample_training_config()
        logger.debug(f"配置内容生成完成，包含 {len(config)} 个部分")

        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(config, f, ensure_ascii=False, indent=2)

        logger.info(f"示例配置文件生成成功: {output_file}")
        print(f"✅ 示例配置文件已生成: {output_file}")
        print(f"📝 您可以编辑此文件，然后使用: python train_vanna.py -c {output_file}")

    except Exception as e:
        logger.error(f"生成示例配置失败: {e}", exc_info=True)
        print(f"❌ 生成示例配置失败: {e}")


def show_training_info(trainer: VannaTrainer):
    """显示训练信息"""
    logger.info("获取 Vanna 训练信息")
    info = trainer.get_training_info()
    logger.debug(f"训练信息: {info}")

    print("📊 Vanna 训练信息")
    print("=" * 40)

    if info.get("connected"):
        logger.info("Vanna 连接状态: 已连接")
        print(f"✅ 连接状态: 已连接")
        print(f"🤖 模型: {info.get('model', 'N/A')}")
        print(f"🗄️ 数据库: {info.get('database', 'N/A')}")

        if "error" in info:
            logger.warning(f"Vanna 警告: {info['error']}")
            print(f"⚠️ 警告: {info['error']}")
    else:
        logger.warning("Vanna 连接状态: 未连接")
        print(f"❌ 连接状态: 未连接")


def test_query(trainer: VannaTrainer, question: str):
    """测试查询"""
    logger.info(f"开始测试查询: {question}")
    print(f"🧪 测试查询: {question}")
    print("=" * 50)

    result = trainer.test_query(question)
    logger.debug(f"查询结果: {result}")

    if result["success"]:
        logger.info("查询执行成功")
        print("✅ 查询成功")
        print(f"📊 结果:\n{result['result']}")
    else:
        logger.error(f"查询执行失败: {result['error']}")
        print("❌ 查询失败")
        print(f"错误: {result['error']}")


def train_from_config(trainer: VannaTrainer, config_file: str):
    """从配置文件训练"""
    logger.info(f"开始从配置文件训练: {config_file}")

    if not Path(config_file).exists():
        logger.error(f"配置文件不存在: {config_file}")
        print(f"❌ 配置文件不存在: {config_file}")
        return

    print(f"🚀 开始从配置文件训练: {config_file}")
    print("=" * 50)

    try:
        logger.info("调用训练器执行训练")
        result = trainer.train_from_file(config_file)
        logger.debug(f"训练结果: {result}")

        if result["success"]:
            logger.info(f"训练完成，成功训练 {len(result['trained_items'])} 个项目")
            print("✅ 训练完成")
            print(f"📊 训练项目数: {len(result['trained_items'])}")

            # 显示训练详情
            for item in result["trained_items"]:
                item_desc = item.get("description", item.get("question", "N/A"))
                logger.debug(f"训练项目: {item['type']} - {item_desc}")
                print(f"  ✓ {item['type']}: {item_desc}")

            # 显示错误（如果有）
            if result["errors"]:
                logger.warning(f"训练过程中出现 {len(result['errors'])} 个错误")
                print(f"\n⚠️ 错误数: {len(result['errors'])}")
                for error in result["errors"]:
                    logger.error(f"训练错误: {error}")
                    print(f"  ✗ {error}")
        else:
            error_msg = result.get("error", "未知错误")
            logger.error(f"训练失败: {error_msg}")
            print("❌ 训练失败")
            print(f"错误: {error_msg}")

    except Exception as e:
        logger.error(f"训练过程异常: {e}", exc_info=True)
        print(f"❌ 训练过程异常: {e}")


def interactive_training(trainer: VannaTrainer):
    """交互式训练"""
    print("🎯 Vanna 交互式训练")
    print("=" * 40)
    print("选择操作:")
    print("1. 从 JSON 配置文件训练")
    print("2. 生成示例配置文件")
    print("3. 测试查询")
    print("4. 显示训练信息")
    print("5. 退出")

    while True:
        try:
            choice = input("\n请选择操作 (1-5): ").strip()

            if choice == "1":
                config_file = input("请输入配置文件路径: ").strip()
                if config_file:
                    train_from_config(trainer, config_file)

            elif choice == "2":
                output_file = input(
                    "请输入输出文件名 (默认: training_config.json): "
                ).strip()
                if not output_file:
                    output_file = "training_config.json"
                generate_sample_config(output_file)

            elif choice == "3":
                question = input("请输入测试问题: ").strip()
                if question:
                    test_query(trainer, question)

            elif choice == "4":
                show_training_info(trainer)

            elif choice == "5":
                print("👋 再见！")
                break

            else:
                print("❌ 无效选择，请输入 1-5")

        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 操作失败: {e}")


if __name__ == "__main__":
    main()
