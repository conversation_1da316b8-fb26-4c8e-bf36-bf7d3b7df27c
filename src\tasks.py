"""
任务定义模块 - 定义所有的 CrewAI Tasks
"""
from crewai import Task

def create_data_query_task(topic: str, agent):
    """创建数据查询任务"""
    return Task(
        description=f"""
        根据用户的请求："{topic}"，执行以下数据查询和分析任务：
        
        1. 理解用户的具体需求和分析目标
        2. 确定需要查询的数据类型和范围
        3. 使用数据库工具执行相关的数据查询
        4. 对查询结果进行初步的数据分析
        5. 识别关键指标、趋势和异常
        6. 整理分析结果，为报告撰写提供数据支持
        
        请确保：
        - 查询的数据准确完整
        - 分析逻辑清晰合理
        - 结果包含具体的数字和统计信息
        - 识别出有价值的数据洞察
        """,
        expected_output="""
        一份结构化的数据分析结果，包含：
        1. 查询的具体数据（表格形式）
        2. 关键统计指标
        3. 数据趋势分析
        4. 重要发现和洞察
        5. 数据质量说明
        
        格式要求：使用清晰的标题和列表，便于报告撰写人员使用。
        """,
        agent=agent
    )

def create_report_writing_task(topic: str, agent, context_tasks=None):
    """创建报告撰写任务"""
    return Task(
        description=f"""
        基于数据分析师提供的数据分析结果，撰写一份关于"{topic}"的专业分析报告。
        
        报告要求：
        1. 使用Markdown格式撰写
        2. 结构清晰，包含以下部分：
           - 执行摘要
           - 关键发现
           - 详细分析
           - 趋势洞察
           - 建议和结论
        3. 将数据转化为商业洞察
        4. 提供可行的业务建议
        5. 语言专业但易懂
        
        请确保：
        - 报告逻辑严密，结论有数据支撑
        - 突出最重要的发现和洞察
        - 提供实用的建议和策略
        - 格式美观，便于阅读
        """,
        expected_output="""
        一份完整的Markdown格式分析报告，包含：
        
        # 标题
        ## 执行摘要
        ## 关键发现
        ## 详细分析
        ### 数据概览
        ### 趋势分析
        ### 深度洞察
        ## 建议和结论
        
        报告应该专业、准确、有洞察力，能够为业务决策提供有价值的参考。
        """,
        agent=agent,
        context=context_tasks if context_tasks else []
    )

def create_data_validation_task(topic: str, agent, context_tasks=None):
    """创建数据验证任务（可选）"""
    return Task(
        description=f"""
        验证关于"{topic}"的数据查询结果的准确性和完整性。
        
        验证内容：
        1. 检查数据查询的逻辑正确性
        2. 验证关键统计指标的计算
        3. 识别可能的数据异常或错误
        4. 确认数据的时间范围和覆盖度
        5. 评估数据质量和可信度
        
        如果发现问题，请：
        - 明确指出问题所在
        - 建议改进方案
        - 重新执行必要的查询
        """,
        expected_output="""
        数据验证报告，包含：
        1. 验证结果概述
        2. 发现的问题（如有）
        3. 数据质量评估
        4. 改进建议
        5. 验证后的最终数据确认
        """,
        agent=agent,
        context=context_tasks if context_tasks else []
    )

def create_comprehensive_analysis_task(topic: str, data_agent, report_agent, validation_agent=None):
    """创建综合分析任务流程"""
    # 创建数据查询任务
    data_task = create_data_query_task(topic, data_agent)
    
    # 创建数据验证任务（如果提供了验证智能体）
    validation_task = None
    if validation_agent:
        validation_task = create_data_validation_task(topic, validation_agent, [data_task])
    
    # 创建报告撰写任务
    context_tasks = [validation_task] if validation_task else [data_task]
    report_task = create_report_writing_task(topic, report_agent, context_tasks)
    
    # 返回任务列表
    tasks = [data_task]
    if validation_task:
        tasks.append(validation_task)
    tasks.append(report_task)
    
    return tasks
