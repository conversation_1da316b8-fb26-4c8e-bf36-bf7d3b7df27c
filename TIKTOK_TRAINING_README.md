# 🎯 TikTok 数据分析训练配置说明

## 📋 概述

已根据您的数据库表结构生成了完整的 Vanna 训练配置文件 `training_config.json`，包含了针对 TikTok 达人和作品数据的专业分析能力。

## 🗄️ 数据库表结构

### 1. at_tiktok_author_pool (达人信息表)
- **主要字段**：
  - `author_id`: 发布账号ID
  - `unique_id`: 用户名
  - `author_name`: 作者昵称
  - `follower_count`: 粉丝数
  - `heart_count`: 总获赞数
  - `is_verified`: 是否认证
  - `commerce_user`: 是否开通电商功能
  - `tt_seller`: 是否TikTok小店卖家

### 2. at_tiktok_author_work_record (作品记录表)
- **主要字段**：
  - `work_id`: 作品唯一标识
  - `author_id`: 发布账号ID
  - `play_count`: 播放量
  - `like_count`: 点赞数
  - `comment_count`: 评论数
  - `share_count`: 转发数
  - `hashtags`: 话题标签
  - `is_ad`: 是否广告

## 🎯 训练内容

### DDL 定义 (2个表)
- TikTok达人信息表完整结构
- TikTok达人作品主表完整结构

### SQL 示例 (12个查询)
1. **热门达人查询** - 查询粉丝数最多的达人
2. **时间范围统计** - 统计特定时间段的作品数量
3. **聚合查询** - 计算达人总播放量
4. **高播放量作品** - 查找爆款作品
5. **电商达人分析** - 分析开通电商功能的达人
6. **互动率计算** - 计算作品互动率
7. **地区分布统计** - 统计各地区达人数量
8. **认证达人分析** - 分析认证达人表现
9. **广告作品分析** - 分析广告作品效果
10. **TikTok小店分析** - 分析小店卖家数据
11. **热门话题分析** - 分析热门标签使用
12. **影响力分析** - 分析达人影响力指标

### 文档说明 (8个方面)
- 达人信息表字段说明
- 作品记录表字段说明
- 电商功能字段说明
- 时间字段格式说明
- 互动率计算公式
- 话题标签处理方法
- 地区语言字段说明
- 状态字段含义

### 分析计划 (4个场景)
1. **达人内容策略分析** - 全面分析达人表现和策略
2. **热门作品特征分析** - 研究爆款内容规律
3. **电商达人分析** - 专门分析电商相关数据
4. **地区语言分析** - 分析地域和语言分布

## 🚀 使用方法

### 1. 测试配置文件
```bash
python test_training_config.py
```

### 2. 开始训练 (需要先安装依赖)
```bash
# 安装依赖
pip install -r requirements.txt

# 开始训练
python train_vanna.py --config training_config.json
```

### 3. 测试查询效果
```bash
python train_vanna.py --test "查询粉丝数最多的达人"
```

## 💡 查询示例

训练完成后，您可以用自然语言提问：

### 基础查询
- "查询粉丝数最多的10个达人"
- "统计上周发布的作品数量"
- "查找播放量超过100万的作品"

### 高级分析
- "分析电商达人的平均粉丝数和表现"
- "查询认证达人的作品互动率"
- "分析TikTok小店卖家的内容策略"

### 趋势分析
- "分析最近一个月的热门话题标签"
- "对比不同地区达人的内容偏好"
- "研究广告作品与普通作品的表现差异"

## 🔧 自定义扩展

### 添加新的查询示例
在 `training_config.json` 的 `sql_examples` 部分添加：

```json
{
  "question": "您的问题",
  "sql": "对应的SQL查询",
  "description": "查询描述"
}
```

### 添加业务文档
在 `documentation` 部分添加业务规则和字段说明。

### 添加分析计划
在 `plans` 部分添加复杂分析的步骤规划。

## ⚠️ 注意事项

### 时间字段处理
- `register_time` 和 `publish_time` 是时间戳，需要用 `FROM_UNIXTIME()` 转换
- `create_time` 和 `update_time` 是 datetime 格式，可直接使用

### JSON 字段处理
- `hashtags` 字段存储 JSON 数组格式的标签
- 分析时可能需要使用 JSON 函数或字符串匹配

### 数据过滤
- 记得使用 `is_del = 0` 过滤已删除的记录
- 根据 `status` 字段筛选监控中的达人

## 📊 预期效果

训练完成后，Vanna 将能够：

1. **理解业务术语** - 识别"达人"、"作品"、"互动率"等概念
2. **处理复杂查询** - 自动生成多表关联和聚合查询
3. **时间范围分析** - 正确处理"上周"、"最近一个月"等时间表达
4. **电商分析** - 理解电商相关字段和业务逻辑
5. **地区分析** - 处理地区和语言相关的查询
6. **趋势分析** - 生成复杂的趋势分析SQL

现在您可以开始训练，让 AI 更好地理解您的 TikTok 数据！
