"""
快速训练 Vanna 模型
解决 SQL 生成问题
"""

import os
import sys
import json

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.config import VANNA_CONFIG, VANNA_DB_CONFIG


def create_vanna_instance():
    """创建 Vanna 实例"""
    print("🔧 创建 Vanna 实例...")
    
    try:
        # 检查是否有 Vanna API key
        vanna_api_key = VANNA_CONFIG.get("api_key")
        vanna_model = VANNA_CONFIG.get("model")
        
        if vanna_api_key and vanna_model:
            print("📡 使用 Vanna 云端模式")
            from vanna.remote import VannaDefault
            vn = VannaDefault(model=vanna_model, api_key=vanna_api_key)
        else:
            print("💻 使用 Vanna 本地模式")
            openai_api_key = os.getenv("OPENAI_API_KEY")
            base_url = os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1")
            
            if not openai_api_key:
                print("❌ 缺少 OPENAI_API_KEY 环境变量")
                return None
            
            from vanna.openai import OpenAI_Chat
            from vanna.chromadb import ChromaDB_VectorStore
            
            class LocalContext_OpenAI(ChromaDB_VectorStore, OpenAI_Chat):
                def __init__(self, config=None):
                    ChromaDB_VectorStore.__init__(self, config=config)
                    OpenAI_Chat.__init__(self, config=config)
            
            openai_config = {
                "api_key": openai_api_key,
                "model": "gpt-3.5-turbo",
                "base_url": base_url
            }
            
            vn = LocalContext_OpenAI(config=openai_config)
        
        # 连接数据库
        vn.connect_to_mysql(**VANNA_DB_CONFIG)
        
        # 设置权限
        vn.allow_llm_to_see_data = True
        
        print("✅ Vanna 实例创建成功")
        return vn
        
    except Exception as e:
        print(f"❌ Vanna 实例创建失败: {e}")
        return None


def quick_train(vn):
    """快速训练 Vanna 模型"""
    print("\n🎓 快速训练 Vanna 模型...")
    
    try:
        # 基本的 DDL 训练
        print("📋 训练表结构...")
        
        ddl_statements = [
            """CREATE TABLE `at_tiktok_author_pool` (
                `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                `author_id` varchar(64) NOT NULL COMMENT '发布账号ID',
                `unique_id` varchar(128) NOT NULL COMMENT '用户名',
                `author_name` varchar(255) DEFAULT NULL COMMENT '作者昵称',
                `follower_count` int DEFAULT '0' COMMENT '粉丝数',
                `heart_count` int DEFAULT '0' COMMENT '总获赞数',
                `video_count` int DEFAULT '0' COMMENT '发布视频数',
                `is_verified` tinyint(1) DEFAULT '0' COMMENT '是否认证(1:是,0:否)',
                `region` varchar(32) DEFAULT NULL COMMENT '用户注册地区',
                `is_del` tinyint DEFAULT '0' COMMENT '是否删除（0未删除，1已删除）',
                PRIMARY KEY (`id`)
            ) COMMENT='TikTok达人信息表';""",
            
            """CREATE TABLE `at_tiktok_author_work_record` (
                `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                `work_id` varchar(64) NOT NULL COMMENT '作品唯一标识',
                `author_id` varchar(64) NOT NULL COMMENT '发布账号ID',
                `unique_id` varchar(128) NOT NULL COMMENT '作者唯一用户名',
                `title` text COMMENT '作品标题',
                `play_count` int DEFAULT '0' COMMENT '播放量',
                `like_count` int DEFAULT '0' COMMENT '点赞数',
                `comment_count` int DEFAULT '0' COMMENT '评论数',
                `share_count` int DEFAULT '0' COMMENT '转发数',
                `publish_time` int DEFAULT NULL COMMENT '发布时间戳',
                PRIMARY KEY (`id`)
            ) COMMENT='TikTok达人作品主表';"""
        ]
        
        for ddl in ddl_statements:
            vn.train(ddl=ddl)
            print("  ✅ 训练了一个表结构")
        
        # 基本的 SQL 示例训练
        print("\n🔍 训练查询示例...")
        
        sql_examples = [
            {
                "question": "查询粉丝数最多的10个达人",
                "sql": "SELECT unique_id, author_name, follower_count FROM at_tiktok_author_pool WHERE is_del = 0 ORDER BY follower_count DESC LIMIT 10;"
            },
            {
                "question": "统计达人总数",
                "sql": "SELECT COUNT(*) as total_authors FROM at_tiktok_author_pool WHERE is_del = 0;"
            },
            {
                "question": "查询播放量超过100万的作品",
                "sql": "SELECT title, play_count, like_count FROM at_tiktok_author_work_record WHERE play_count > 1000000 ORDER BY play_count DESC;"
            },
            {
                "question": "查询认证达人",
                "sql": "SELECT unique_id, author_name, follower_count FROM at_tiktok_author_pool WHERE is_verified = 1 AND is_del = 0;"
            },
            {
                "question": "统计各地区达人数量",
                "sql": "SELECT region, COUNT(*) as author_count FROM at_tiktok_author_pool WHERE is_del = 0 AND region IS NOT NULL GROUP BY region ORDER BY author_count DESC;"
            }
        ]
        
        for example in sql_examples:
            vn.train(question=example["question"], sql=example["sql"])
            print(f"  ✅ 训练了查询: {example['question']}")
        
        # 训练一些文档说明
        print("\n📚 训练业务文档...")
        
        documentation = [
            "时间字段说明：register_time 和 publish_time 是时间戳格式，需要使用 FROM_UNIXTIME() 函数转换。",
            "互动率计算公式：(like_count + comment_count + share_count) / play_count * 100。",
            "状态字段：is_del 表示是否删除(0:未删除,1:已删除)，is_verified 表示是否认证(1:是,0:否)。"
        ]
        
        for doc in documentation:
            vn.train(documentation=doc)
            print("  ✅ 训练了一条文档")
        
        print("✅ 快速训练完成")
        return True
        
    except Exception as e:
        print(f"❌ 训练失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_trained_model(vn):
    """测试训练后的模型"""
    print("\n🧪 测试训练后的模型...")
    
    test_questions = [
        "查询粉丝数最多的5个达人",
        "统计达人总数",
        "查询认证达人"
    ]
    
    for question in test_questions:
        try:
            print(f"\n🔍 测试问题: {question}")
            sql = vn.generate_sql(question)
            print(f"生成的 SQL: {sql}")
            
            # 检查是否是有效的 SQL
            if sql.upper().strip().startswith('SELECT'):
                print("✅ 生成了有效的 SQL")
                
                # 尝试执行
                try:
                    result = vn.run_sql(sql)
                    print(f"✅ SQL 执行成功，返回 {len(result) if hasattr(result, '__len__') else 'N/A'} 行")
                except Exception as e:
                    print(f"⚠️ SQL 执行失败: {e}")
            else:
                print(f"❌ 生成的不是有效 SQL: {sql}")
                return False
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            return False
    
    print("✅ 所有测试通过")
    return True


def update_global_instance(vn):
    """更新全局实例"""
    print("\n🔄 更新全局 Vanna 实例...")
    
    try:
        import src.tools.database_tools as db_tools
        
        db_tools._global_vn_instance = vn
        db_tools._global_vanna_available = True
        
        print("✅ 全局实例更新成功")
        return True
        
    except Exception as e:
        print(f"❌ 更新失败: {e}")
        return False


def main():
    """主函数"""
    print("🎓 Vanna 快速训练工具")
    print("="*40)
    
    # 创建实例
    vn = create_vanna_instance()
    if not vn:
        print("❌ 无法创建 Vanna 实例")
        return
    
    # 快速训练
    if not quick_train(vn):
        print("❌ 训练失败")
        return
    
    # 测试模型
    if not test_trained_model(vn):
        print("❌ 模型测试失败")
        return
    
    # 更新全局实例
    if not update_global_instance(vn):
        print("❌ 更新全局实例失败")
        return
    
    print("\n🎉 Vanna 模型训练和配置完成！")
    print("\n💡 现在可以正常使用数据库查询功能了")
    print("建议重启 Chainlit 应用: chainlit run app.py")


if __name__ == "__main__":
    main()
