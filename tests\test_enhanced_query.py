"""
测试增强查询功能的示例脚本
演示新的数据格式化和字段映射功能
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.tools.database_tools import DatabaseQueryTool
from src.tools.data_formatter import DataFormatter
from src.tools.data_enhancer import DataEnhancer


def test_data_formatter():
    """测试数据格式化器"""
    print("🧪 测试数据格式化器...")
    
    formatter = DataFormatter()
    
    # 测试字段映射
    print("\n📋 字段映射测试:")
    mappings = formatter.field_mappings
    for table, fields in mappings.items():
        print(f"  表 {table}:")
        for orig, trans in list(fields.items())[:5]:  # 只显示前5个
            print(f"    {orig} -> {trans}")
        if len(fields) > 5:
            print(f"    ... 还有 {len(fields) - 5} 个字段")
    
    print("✅ 数据格式化器测试完成")


def test_data_enhancer():
    """测试数据增强器"""
    print("\n🚀 测试数据增强器...")
    
    enhancer = DataEnhancer()
    
    # 测试查询意图分析
    test_queries = [
        "查询粉丝数最多的10个达人",
        "分析播放量超过100万的视频",
        "统计各地区达人的互动率",
        "查看电商达人的带货表现",
        "分析上周发布的热门作品"
    ]
    
    print("\n🔍 查询意图分析测试:")
    for query in test_queries:
        intent, keywords = enhancer.analyze_query_intent(query)
        print(f"  查询: {query}")
        print(f"  意图: {intent}")
        print(f"  关键词: {keywords}")
        print()
    
    print("✅ 数据增强器测试完成")


def test_enhanced_database_query():
    """测试增强的数据库查询"""
    print("\n💾 测试增强数据库查询...")
    
    try:
        # 创建查询工具
        query_tool = DatabaseQueryTool()
        
        # 测试查询
        test_queries = [
            "查询粉丝数最多的5个达人",
            "统计各地区达人数量",
            "查看播放量超过50万的作品"
        ]
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n📊 测试查询 {i}: {query}")
            print("-" * 50)
            
            try:
                result = query_tool._run(query)
                print(result)
            except Exception as e:
                print(f"❌ 查询失败: {e}")
            
            print("\n" + "="*80)
    
    except Exception as e:
        print(f"❌ 数据库查询工具初始化失败: {e}")
        print("💡 请确保:")
        print("  1. 数据库连接配置正确")
        print("  2. Vanna 模型已训练")
        print("  3. 环境变量已设置")


def test_configuration_loading():
    """测试配置文件加载"""
    print("\n⚙️ 测试配置文件加载...")
    
    try:
        enhancer = DataEnhancer()
        config = enhancer.config
        
        print("📁 配置文件内容:")
        print(f"  字段映射表数量: {len(config.get('field_mappings', {}))}")
        print(f"  数据增强器数量: {len(config.get('data_enhancers', {}))}")
        print(f"  查询模板数量: {len(config.get('query_templates', {}))}")
        
        # 显示数据增强器类型
        enhancers = config.get('data_enhancers', {})
        print(f"\n🔧 可用的数据增强器:")
        for name, config_item in enhancers.items():
            print(f"  - {name}: {config_item.get('description', '无描述')}")
        
        print("✅ 配置文件加载测试完成")
        
    except Exception as e:
        print(f"❌ 配置文件加载失败: {e}")


def main():
    """主测试函数"""
    print("🎯 TikTok 数据分析增强功能测试")
    print("="*60)
    
    # 测试各个组件
    test_configuration_loading()
    test_data_formatter()
    test_data_enhancer()
    
    # 询问是否测试数据库查询
    print("\n" + "="*60)
    response = input("是否测试数据库查询功能？(需要数据库连接) [y/N]: ")
    
    if response.lower() in ['y', 'yes', '是']:
        test_enhanced_database_query()
    else:
        print("⏭️ 跳过数据库查询测试")
    
    print("\n🎉 所有测试完成！")
    print("\n📝 使用说明:")
    print("1. 新的查询工具会自动将数据库字段转换为中文说明")
    print("2. 根据查询意图自动补充相关字段")
    print("3. 提供结构化的数据返回格式")
    print("4. 自动计算衍生指标（如互动率）")
    print("\n💡 示例查询:")
    print("- '查询粉丝数最多的达人' -> 自动补充用户名、昵称、获赞数等")
    print("- '分析热门作品' -> 自动补充标题、作者、互动数据等")
    print("- '统计互动数据' -> 自动计算互动率、点赞率等指标")


if __name__ == "__main__":
    main()
