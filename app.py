"""
Chainlit 应用主入口文件
TikTok 数据分析智能体的前端界面
"""

import chainlit as cl
import asyncio
from src.crew import create_simple_crew, create_enhanced_crew, CrewManager
from src.config import validate_config, get_available_models
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 全局团队管理器
crew_manager = CrewManager()


@cl.on_chat_start
async def start():
    """聊天开始时的初始化"""
    try:
        # 验证配置
        validate_config()

        # 获取可用模型
        available_models = get_available_models()

        # 构建模型信息
        model_info = ""
        if available_models:
            model_info = "\n## 🤖 **可用模型**\n"
            for model in available_models:
                model_info += (
                    f"- **{model['description']}**: {', '.join(model['models'])}\n"
                )

        # 发送欢迎消息
        welcome_message = f"""
            # 🎯 TikTok 数据分析智能体

            欢迎使用 TikTok 数据分析智能体！我可以帮助您：

            ## 🔍 **分析能力**
            - 📊 达人数据分析（粉丝增长、内容表现等）
            - 🎬 视频内容分析（播放量、互动率等）
            - 📈 趋势洞察（热门话题、增长趋势等）
            - 🎯 用户画像分析（粉丝特征、偏好等）

            ## 💡 **使用示例**

            ### 🔍 **简单查询**（直接返回数据）
            - "查询粉丝数最多的5个达人"
            - "统计各地区达人数量"
            - "显示播放量超过100万的视频"

            ### 📊 **分析报告**（生成完整报告）
            - "分析上周粉丝增长最快的达人"
            - "分析美妆类达人的内容策略"
            - "研究最近一个月的热门话题趋势"

            ## ⚙️ **工作模式**
            - **智能模式**: 根据问题自动选择查询或分析
            - **深度模式**: 使用"深度模式 [问题]"强制生成详细报告

            ## 🎛️ **模型选择**
            - **自动模式**: 系统自动选择最佳可用模型
            - **指定模型**: 使用 "通义模式" 或 "openai模式" 指定模型提供商
            {model_info}
            请输入您想要分析的问题，我将为您提供专业的数据分析报告！
        """

        await cl.Message(content=welcome_message).send()

        # 设置用户会话状态
        cl.user_session.set("analysis_count", 0)
        cl.user_session.set("crew_manager", crew_manager)

    except Exception as e:
        error_message = (
            f"❌ 初始化失败: {str(e)}\n\n请检查环境配置文件 (.env) 是否正确设置。"
        )
        await cl.Message(content=error_message).send()


@cl.on_message
async def main(message: cl.Message):
    """处理用户消息"""
    user_input = message.content.strip()

    if not user_input:
        await cl.Message(content="请输入您想要分析的问题。").send()
        return

    # 获取用户会话状态
    analysis_count = cl.user_session.get("analysis_count", 0)
    crew_manager = cl.user_session.get("crew_manager")

    # 更新分析计数
    analysis_count += 1
    cl.user_session.set("analysis_count", analysis_count)

    # 检查是否是特殊命令
    if user_input.lower() in ["help", "帮助", "h"]:
        await show_help()
        return

    # 解析模式和模型提供商
    mode = "simple"
    model_provider = "auto"
    topic = user_input

    # 判断用户意图：是否需要生成报告
    need_report = detect_report_intent(user_input)

    # 检查深度模式
    if user_input.lower().startswith("深度模式") or user_input.lower().startswith(
        "enhanced"
    ):
        mode = "enhanced"
        need_report = True  # 深度模式默认生成报告
        topic = user_input.replace("深度模式", "").replace("enhanced", "").strip()
        if not topic:
            await cl.Message(
                content="请在'深度模式'后面输入您要分析的具体问题。"
            ).send()
            return

    # 检查模型提供商
    if user_input.lower().startswith("通义模式") or user_input.lower().startswith(
        "tongyi"
    ):
        model_provider = "tongyi"
        topic = user_input.replace("通义模式", "").replace("tongyi", "").strip()
        if not topic:
            await cl.Message(
                content="请在'通义模式'后面输入您要分析的具体问题。"
            ).send()
            return
    elif user_input.lower().startswith("openai模式") or user_input.lower().startswith(
        "gpt"
    ):
        model_provider = "openai"
        topic = user_input.replace("openai模式", "").replace("gpt", "").strip()
        if not topic:
            await cl.Message(
                content="请在'openai模式'后面输入您要分析的具体问题。"
            ).send()
            return

    # 组合模式检查（如：深度模式 通义模式 分析问题）
    if "通义模式" in topic or "tongyi" in topic.lower():
        model_provider = "tongyi"
        topic = topic.replace("通义模式", "").replace("tongyi", "").strip()
    elif "openai模式" in topic or "gpt" in topic.lower():
        model_provider = "openai"
        topic = topic.replace("openai模式", "").replace("gpt", "").strip()

    if not topic:
        await cl.Message(content="请输入您想要分析的具体问题。").send()
        return

    # 运行分析
    if need_report:
        await run_analysis(topic, mode=mode, model_provider=model_provider)
    else:
        await run_simple_query(topic, model_provider=model_provider)


async def run_analysis(topic: str, mode: str = "simple", model_provider: str = "auto"):
    """运行数据分析"""
    # 显示分析开始消息
    mode_name = "深度分析" if mode == "enhanced" else "快速分析"
    provider_name = {
        "auto": "自动选择",
        "tongyi": "通义千问",
        "openai": "OpenAI GPT",
    }.get(model_provider, model_provider)

    start_message = f"🚀 开始{mode_name}：**{topic}**\n\n🤖 使用模型：{provider_name}\n正在组建分析团队..."

    msg = cl.Message(content=start_message)
    await msg.send()

    try:
        # 创建分析步骤
        async with cl.Step(name="团队组建", type="tool") as step:
            step.output = (
                f"正在创建数据分析师和报告撰写专家（使用{provider_name}模型）..."
            )

            # 创建团队
            if mode == "enhanced":
                crew = create_enhanced_crew(topic, model_provider)
            else:
                crew = create_simple_crew(topic, model_provider)

        async with cl.Step(name="数据查询与分析", type="tool") as step:
            step.output = "数据分析师正在查询和分析相关数据..."

            # 在后台线程中运行 crew.kickoff()
            def run_crew():
                return crew.kickoff()

            # 使用 asyncio 在线程池中运行
            crew_result = await asyncio.get_event_loop().run_in_executor(None, run_crew)

        async with cl.Step(name="报告生成", type="tool") as step:
            step.output = "报告撰写专家正在整理分析结果..."

        # 处理 CrewAI 结果
        # 记录结果类型用于调试
        logger.info(f"CrewAI 结果类型: {type(crew_result)}")
        logger.info(f"CrewAI 结果属性: {dir(crew_result)}")

        # CrewAI 的 kickoff() 返回的可能是 CrewOutput 对象
        if hasattr(crew_result, "raw"):
            # 如果是 CrewOutput 对象，提取 raw 内容
            result_content = crew_result.raw
            logger.info("使用 crew_result.raw")
        elif hasattr(crew_result, "result"):
            # 如果有 result 属性
            result_content = crew_result.result
            logger.info("使用 crew_result.result")
        elif hasattr(crew_result, "output"):
            # 如果有 output 属性
            result_content = crew_result.output
            logger.info("使用 crew_result.output")
        elif hasattr(crew_result, "tasks_output"):
            # 如果有 tasks_output 属性（CrewAI 新版本）
            if crew_result.tasks_output:
                # 获取最后一个任务的输出
                last_task_output = crew_result.tasks_output[-1]
                if hasattr(last_task_output, "raw"):
                    result_content = last_task_output.raw
                else:
                    result_content = str(last_task_output)
            else:
                result_content = "任务执行完成，但没有输出内容"
            logger.info("使用 crew_result.tasks_output")
        else:
            # 直接转换为字符串
            result_content = str(crew_result)
            logger.info("直接转换为字符串")

        # 确保结果不为空
        if not result_content or result_content.strip() == "":
            result_content = (
                "分析完成，但没有生成具体内容。请尝试重新提问或使用更具体的问题。"
            )

        # 发送最终结果
        final_message = f"""
# 📋 分析报告完成

{result_content}

---
💡 **提示**: 您可以继续提问进行更深入的分析，或者使用"深度模式 [问题]"获得更详细的分析结果。
        """

        await cl.Message(content=final_message).send()

    except Exception as e:
        error_message = f"""
❌ **分析过程中出现错误**

错误信息: {str(e)}

**可能的解决方案:**
1. 检查数据库连接是否正常
2. 确认 API 密钥是否正确配置
3. 验证查询的数据是否存在
4. 尝试重新表述您的问题

请重新尝试或联系管理员。
        """
        await cl.Message(content=error_message).send()


async def show_help():
    """显示帮助信息"""
    help_message = """
# 📚 使用帮助

## 🎯 **基本用法**

### 🔍 **简单查询**（快速获取数据）
- "查询粉丝数最多的5个达人"
- "统计各地区达人数量"
- "显示播放量超过100万的视频"

### 📊 **分析报告**（生成完整分析）
- "分析上周粉丝增长最快的达人"
- "分析美妆类达人的内容策略"
- "研究最近一个月的热门话题趋势"

## 🔧 **高级功能**
- **智能识别**: 系统自动判断是简单查询还是需要分析报告
- **深度模式**: 输入 "深度模式 [您的问题]" 强制生成详细报告
- **模型选择**:
  - "通义模式 [您的问题]" - 使用阿里云通义千问模型
  - "openai模式 [您的问题]" - 使用 OpenAI GPT 模型
- **组合使用**: "深度模式 通义模式 [您的问题]" - 深度分析 + 指定模型
- **帮助**: 输入 "help" 或 "帮助" 查看此帮助信息

## 📊 **分析类型**
1. **达人分析**: 粉丝数据、内容表现、增长趋势
2. **内容分析**: 视频数据、互动指标、热门内容
3. **趋势分析**: 话题趋势、行业洞察、市场变化
4. **用户分析**: 粉丝画像、行为偏好、互动模式

## 🤖 **模型特点**
- **通义千问**: 中文理解能力强，适合中文内容分析
- **OpenAI GPT**: 逻辑推理能力强，适合复杂数据分析
- **自动选择**: 系统根据可用性自动选择最佳模型

## ⚡ **使用技巧**
- 问题越具体，分析结果越准确
- 可以指定时间范围（如"上周"、"最近一个月"）
- 可以指定分析维度（如"按类别"、"按地区"）
- 根据分析内容选择合适的模型

有任何问题都可以直接提问！
    """
    await cl.Message(content=help_message).send()


def detect_report_intent(user_input: str) -> bool:
    """
    检测用户意图是否需要生成完整报告

    Args:
        user_input (str): 用户输入

    Returns:
        bool: True 表示需要生成报告，False 表示只需要简单查询
    """
    # 需要报告的关键词
    report_keywords = [
        "分析",
        "报告",
        "总结",
        "洞察",
        "趋势",
        "策略",
        "建议",
        "特点",
        "特征",
        "模式",
        "规律",
        "对比",
        "比较",
        "深度",
        "详细",
        "全面",
        "综合",
        "评估",
        "研究",
    ]

    # 简单查询的关键词
    query_keywords = [
        "查询",
        "查看",
        "显示",
        "列出",
        "统计",
        "计算",
        "多少",
        "几个",
        "数量",
        "排名",
        "前",
        "最",
    ]

    user_lower = user_input.lower()

    # 如果包含报告关键词，需要生成报告
    for keyword in report_keywords:
        if keyword in user_lower:
            return True

    # 如果只包含查询关键词且没有报告关键词，只需要简单查询
    has_query_keyword = any(keyword in user_lower for keyword in query_keywords)
    if has_query_keyword:
        return False

    # 默认情况下，如果意图不明确，生成报告（保守策略）
    return True


async def run_simple_query(topic: str, model_provider: str = "auto"):
    """运行简单数据查询（不生成完整报告）"""
    from src.tools.database_tools import DatabaseQueryTool, DatabaseConnectionTool

    # 显示查询开始消息
    start_message = f"🔍 正在查询：**{topic}**\n\n正在连接数据库..."

    msg = cl.Message(content=start_message)
    await msg.send()

    try:
        async with cl.Step(name="数据查询", type="tool") as step:
            step.output = "正在执行数据库查询..."

            # 创建数据库查询工具
            query_tool = DatabaseQueryTool()

            # 执行查询
            def run_query():
                return query_tool._run(topic)

            # 在后台线程中运行查询
            result = await asyncio.get_event_loop().run_in_executor(None, run_query)

        # 发送查询结果
        final_message = f"""
# 🔍 查询结果

{result}

---
💡 **提示**: 如果您需要更详细的分析报告，可以使用"分析 [问题]"或"深度模式 [问题]"。
        """

        await cl.Message(content=final_message).send()

    except Exception as e:
        error_message = f"""
❌ **查询过程中出现错误**

错误信息: {str(e)}

**可能的解决方案:**
1. 检查数据库连接是否正常
2. 确认查询的数据是否存在
3. 尝试重新表述您的问题

请重新尝试或联系管理员。
        """
        await cl.Message(content=error_message).send()


if __name__ == "__main__":
    # 这里可以添加一些启动前的检查
    print("🚀 启动 TikTok 数据分析智能体...")
    print("📝 请确保已正确配置 .env 文件")
    print("🌐 应用将在 http://localhost:8000 启动")
