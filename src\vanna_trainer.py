"""
Vanna 训练模块 - 独立的训练逻辑
支持通过 JSON 配置进行训练
"""

import json
import os
from typing import Dict, List, Optional
from .config import DATABASE_CONFIG, VANNA_CONFIG, VANNA_DB_CONFIG
import logging

logger = logging.getLogger(__name__)


class VannaTrainer:
    """Vanna 训练器"""

    def __init__(self):
        self.vn_instance = None
        self.is_connected = False
        self._setup_vanna()

    def _setup_vanna(self):
        """设置 Vanna 实例"""
        try:
            # 检查是否有 Vanna API key
            vanna_api_key = VANNA_CONFIG.get("api_key")
            vanna_model = VANNA_CONFIG.get("model")

            if vanna_api_key and vanna_api_key.strip():
                # 使用 Vanna Cloud
                logger.info("检测到 Vanna API Key，使用远程模式...")
                from vanna.remote import VannaDefault

                self.vn_instance = VannaDefault(
                    model=vanna_model, api_key=vanna_api_key
                )
                logger.info("使用 Vanna Cloud 模式")
            else:
                # 使用本地模式
                logger.info("未检测到 Vanna API Key，使用本地模式...")
                openai_key = os.getenv("OPENAI_API_KEY")

                if not openai_key:
                    raise ValueError("使用本地模式需要 OPENAI_API_KEY")

                from vanna.local import LocalContext_OpenAI

                # 准备 OpenAI 配置
                openai_config = {
                    "api_key": openai_key,
                    "temperature": 0.1,
                }

                # 检查是否有自定义 base_url
                base_url = os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1")
                if base_url != "https://api.openai.com/v1":
                    logger.info(f"使用自定义 OpenAI base_url: {base_url}")
                    openai_config["base_url"] = base_url

                self.vn_instance = LocalContext_OpenAI(config=openai_config)
                logger.info(f"使用 Vanna 本地模式 (base_url: {base_url})")

            # 连接数据库
            try:
                # 验证数据库配置
                required_fields = ["host", "database", "user", "password"]
                for field in required_fields:
                    value = DATABASE_CONFIG.get(field)
                    if not value or (isinstance(value, str) and value.strip() == ""):
                        raise ValueError(f"数据库配置缺少必要字段: {field}")

                # 检查是否使用默认密码
                password = DATABASE_CONFIG.get("password", "")
                if password == "your_password":
                    raise ValueError("请在 .env 文件中设置正确的数据库密码")

                logger.info(
                    f"正在连接数据库: {DATABASE_CONFIG['host']}:{DATABASE_CONFIG['port']}/{DATABASE_CONFIG['database']}"
                )

                self.vn_instance.connect_to_mysql(**VANNA_DB_CONFIG)

                # 设置 Vanna 权限配置
                self.vn_instance.allow_llm_to_see_data = True
                logger.info("已启用 Vanna LLM 数据库内省功能")

                logger.info("数据库连接成功")

            except Exception as db_error:
                logger.error(f"数据库连接失败: {db_error}")
                raise db_error

            self.is_connected = True
            logger.info("Vanna 连接成功")

        except Exception as e:
            logger.error(f"Vanna 设置失败: {e}")
            self.is_connected = False

    def train_from_json(self, training_config: Dict) -> Dict[str, any]:
        """
        从 JSON 配置进行训练

        Args:
            training_config (Dict): 训练配置

        Returns:
            Dict: 训练结果
        """
        if not self.is_connected:
            return {"success": False, "error": "Vanna 未连接"}

        results = {"success": True, "trained_items": [], "errors": []}

        try:
            # 训练 DDL
            if "ddl" in training_config:
                for ddl_item in training_config["ddl"]:
                    try:
                        if isinstance(ddl_item, dict):
                            ddl_sql = ddl_item.get("sql", "")
                            description = ddl_item.get("description", "")
                        else:
                            ddl_sql = ddl_item
                            description = ""

                        self.vn_instance.train(ddl=ddl_sql)
                        results["trained_items"].append(
                            {
                                "type": "ddl",
                                "description": description,
                                "status": "success",
                            }
                        )
                        logger.info(f"DDL 训练成功: {description}")

                    except Exception as e:
                        error_msg = f"DDL 训练失败: {str(e)}"
                        results["errors"].append(error_msg)
                        logger.error(error_msg)

            # 训练 SQL 示例
            if "sql_examples" in training_config:
                for sql_item in training_config["sql_examples"]:
                    try:
                        if isinstance(sql_item, dict):
                            sql = sql_item.get("sql", "")
                            question = sql_item.get("question", "")
                            description = sql_item.get("description", "")
                        else:
                            sql = sql_item
                            question = ""
                            description = ""

                        if question:
                            self.vn_instance.train(question=question, sql=sql)
                        else:
                            self.vn_instance.train(sql=sql)

                        results["trained_items"].append(
                            {
                                "type": "sql",
                                "question": question,
                                "description": description,
                                "status": "success",
                            }
                        )
                        logger.info(f"SQL 训练成功: {description or question}")

                    except Exception as e:
                        error_msg = f"SQL 训练失败: {str(e)}"
                        results["errors"].append(error_msg)
                        logger.error(error_msg)

            # 训练文档
            if "documentation" in training_config:
                for doc_item in training_config["documentation"]:
                    try:
                        if isinstance(doc_item, dict):
                            doc_text = doc_item.get("content", "")
                            description = doc_item.get("description", "")
                        else:
                            doc_text = doc_item
                            description = ""

                        self.vn_instance.train(documentation=doc_text)
                        results["trained_items"].append(
                            {
                                "type": "documentation",
                                "description": description,
                                "status": "success",
                            }
                        )
                        logger.info(f"文档训练成功: {description}")

                    except Exception as e:
                        error_msg = f"文档训练失败: {str(e)}"
                        results["errors"].append(error_msg)
                        logger.error(error_msg)

            # 训练计划
            if "plans" in training_config:
                for plan_item in training_config["plans"]:
                    try:
                        if isinstance(plan_item, dict):
                            question = plan_item.get("question", "")
                            plan = plan_item.get("plan", "")
                            description = plan_item.get("description", "")
                        else:
                            continue  # 计划必须是字典格式

                        self.vn_instance.train(question=question, plan=plan)
                        results["trained_items"].append(
                            {
                                "type": "plan",
                                "question": question,
                                "description": description,
                                "status": "success",
                            }
                        )
                        logger.info(f"计划训练成功: {description or question}")

                    except Exception as e:
                        error_msg = f"计划训练失败: {str(e)}"
                        results["errors"].append(error_msg)
                        logger.error(error_msg)

        except Exception as e:
            results["success"] = False
            results["errors"].append(f"训练过程出错: {str(e)}")
            logger.error(f"训练过程出错: {str(e)}")

        return results

    def train_from_file(self, json_file_path: str) -> Dict[str, any]:
        """
        从 JSON 文件进行训练

        Args:
            json_file_path (str): JSON 文件路径

        Returns:
            Dict: 训练结果
        """
        try:
            with open(json_file_path, "r", encoding="utf-8") as f:
                training_config = json.load(f)

            return self.train_from_json(training_config)

        except Exception as e:
            return {"success": False, "error": f"读取训练文件失败: {str(e)}"}

    def get_training_info(self) -> Dict[str, any]:
        """获取训练信息"""
        if not self.is_connected:
            return {"connected": False}

        try:
            # 获取训练的信息（如果 Vanna 支持的话）
            return {
                "connected": True,
                "model": VANNA_CONFIG["model"],
                "database": DATABASE_CONFIG["database"],
            }
        except Exception as e:
            return {"connected": True, "error": f"获取训练信息失败: {str(e)}"}

    def test_query(self, question: str) -> Dict[str, any]:
        """
        测试查询

        Args:
            question (str): 测试问题

        Returns:
            Dict: 查询结果
        """
        if not self.is_connected:
            return {"success": False, "error": "Vanna 未连接"}

        try:
            result = self.vn_instance.ask(question)
            return {"success": True, "question": question, "result": str(result)}
        except Exception as e:
            return {"success": False, "question": question, "error": str(e)}


def create_sample_training_config() -> Dict:
    """创建示例训练配置"""
    return {
        "ddl": [
            {
                "sql": """
                CREATE TABLE creators (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    username VARCHAR(100) UNIQUE,
                    display_name VARCHAR(200),
                    followers_count INT DEFAULT 0,
                    following_count INT DEFAULT 0,
                    likes_count BIGINT DEFAULT 0,
                    videos_count INT DEFAULT 0,
                    category VARCHAR(50),
                    verified BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                );
                """,
                "description": "创作者表结构",
            },
            {
                "sql": """
                CREATE TABLE videos (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    creator_id INT,
                    title TEXT,
                    description TEXT,
                    view_count BIGINT DEFAULT 0,
                    like_count INT DEFAULT 0,
                    comment_count INT DEFAULT 0,
                    share_count INT DEFAULT 0,
                    duration INT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (creator_id) REFERENCES creators(id)
                );
                """,
                "description": "视频表结构",
            },
        ],
        "sql_examples": [
            {
                "question": "查询粉丝数最多的10个达人",
                "sql": "SELECT username, display_name, followers_count FROM creators ORDER BY followers_count DESC LIMIT 10;",
                "description": "热门达人查询",
            },
            {
                "question": "统计上周发布的视频数量",
                "sql": "SELECT COUNT(*) as total_videos FROM videos WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY);",
                "description": "时间范围统计",
            },
            {
                "question": "查询每个达人的总播放量",
                "sql": "SELECT c.username, c.display_name, SUM(v.view_count) as total_views FROM creators c JOIN videos v ON c.id = v.creator_id GROUP BY c.id ORDER BY total_views DESC;",
                "description": "聚合查询示例",
            },
        ],
        "documentation": [
            {
                "content": "creators 表存储 TikTok 达人的基本信息，包括用户名、显示名称、粉丝数、关注数等。verified 字段表示是否为认证用户。",
                "description": "creators 表说明",
            },
            {
                "content": "videos 表存储视频信息，通过 creator_id 与 creators 表关联。包含播放量、点赞数、评论数、分享数等互动数据。",
                "description": "videos 表说明",
            },
        ],
    }
