"""
详细调试 Vanna 连接问题
"""

import os
import traceback
from dotenv import load_dotenv


def debug_step_by_step():
    """逐步调试 Vanna 设置过程"""
    print("🔍 逐步调试 Vanna 设置过程")
    print("=" * 60)

    # 加载环境变量
    load_dotenv()

    try:
        print("1️⃣ 导入配置模块...")
        from src.config import DATABASE_CONFIG, VANNA_CONFIG

        print("   ✅ 配置模块导入成功")

        print("\n2️⃣ 检查数据库配置...")
        print(f"   DATABASE_CONFIG 类型: {type(DATABASE_CONFIG)}")
        for key, value in DATABASE_CONFIG.items():
            if key == "password":
                print(
                    f"   {key}: {'*' * len(str(value)) if value else 'None/Empty'} (类型: {type(value)})"
                )
            else:
                print(f"   {key}: {value} (类型: {type(value)})")

        print("\n3️⃣ 检查 VANNA 配置...")
        print(f"   VANNA_CONFIG 类型: {type(VANNA_CONFIG)}")
        for key, value in VANNA_CONFIG.items():
            if "key" in key.lower():
                print(
                    f"   {key}: {'*' * len(str(value)) if value else 'None/Empty'} (类型: {type(value)})"
                )
            else:
                print(f"   {key}: {value} (类型: {type(value)})")

        print("\n4️⃣ 测试数据库配置验证...")
        required_fields = ["host", "database", "user", "password"]
        for field in required_fields:
            value = DATABASE_CONFIG.get(field)
            print(f"   {field}: {repr(value)} (类型: {type(value)})")
            if not value or (isinstance(value, str) and value.strip() == ""):
                print(f"   ❌ 字段 {field} 验证失败")
                return False
            else:
                print(f"   ✅ 字段 {field} 验证通过")

        print("\n5️⃣ 测试 Vanna 导入...")

        # 检查是否有 Vanna API key
        vanna_api_key = VANNA_CONFIG.get("api_key")
        vanna_model = VANNA_CONFIG.get("model")

        if vanna_api_key and vanna_api_key.strip():
            print("   检测到 Vanna API Key，使用远程模式...")
            from vanna.remote import VannaDefault

            vn = VannaDefault(model=vanna_model, api_key=vanna_api_key)
            print("   ✅ Vanna 远程实例创建成功")
        else:
            print("   未检测到 Vanna API Key，使用本地模式...")
            from vanna.local import LocalContext_OpenAI

            openai_key = os.getenv("OPENAI_API_KEY")
            base_url = os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1")

            if not openai_key:
                raise Exception("使用本地模式需要 OPENAI_API_KEY")

            # 准备 OpenAI 配置
            openai_config = {"api_key": openai_key, "temperature": 0.1}

            if base_url != "https://api.openai.com/v1":
                print(f"   设置自定义 base_url: {base_url}")
                openai_config["base_url"] = base_url

            vn = LocalContext_OpenAI(config=openai_config)
            print("   ✅ Vanna 本地实例创建成功")

        print("\n6️⃣ 测试 Vanna 实例...")
        print(f"   Vanna 实例类型: {type(vn)}")
        print("   ✅ Vanna 实例验证成功")

        print("\n7️⃣ 测试数据库连接...")
        try:
            vn.connect_to_mysql(
                host=DATABASE_CONFIG["host"],
                dbname=DATABASE_CONFIG["database"],
                user=DATABASE_CONFIG["user"],
                password=DATABASE_CONFIG["password"],
                port=DATABASE_CONFIG["port"],
            )
            print("   ✅ 数据库连接成功")

            # 测试基本查询功能
            print("\n8️⃣ 测试基本查询功能...")
            try:
                # 获取表列表
                tables = vn.run_sql("SHOW TABLES")
                print(f"   ✅ 查询成功，找到 {len(tables)} 个表")
                return True
            except Exception as query_error:
                print(f"   ❌ 查询测试失败: {query_error}")
                return False

        except Exception as db_error:
            print(f"   ❌ 数据库连接失败: {db_error}")
            print(f"   详细错误: {traceback.format_exc()}")
            return False

    except Exception as e:
        print(f"❌ 调试过程中出现错误: {e}")
        print(f"详细错误信息:")
        traceback.print_exc()
        return False


def test_mysql_connection_directly():
    """直接测试 MySQL 连接"""
    print("\n🗄️ 直接测试 MySQL 连接")
    print("=" * 40)

    try:
        from src.config import DATABASE_CONFIG
        import mysql.connector

        print("尝试连接数据库...")
        connection = mysql.connector.connect(
            host=DATABASE_CONFIG["host"],
            database=DATABASE_CONFIG["database"],
            user=DATABASE_CONFIG["user"],
            password=DATABASE_CONFIG["password"],
            port=DATABASE_CONFIG["port"],
        )

        if connection.is_connected():
            cursor = connection.cursor()
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            print(f"✅ MySQL 连接成功！版本: {version[0]}")

            # 测试表是否存在
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            print(f"📋 数据库中的表数量: {len(tables)}")
            for table in tables[:5]:  # 只显示前5个表
                print(f"   - {table[0]}")
            if len(tables) > 5:
                print(f"   ... 还有 {len(tables) - 5} 个表")

            cursor.close()
            connection.close()
            return True
        else:
            print("❌ MySQL 连接失败")
            return False

    except Exception as e:
        print(f"❌ MySQL 连接测试失败: {e}")
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🐛 Vanna 详细调试工具")
    print("=" * 60)

    # 逐步调试
    vanna_success = debug_step_by_step()

    # 直接测试 MySQL
    mysql_success = test_mysql_connection_directly()

    print("\n📊 调试结果总结")
    print("=" * 40)
    print(f"Vanna 设置: {'✅ 成功' if vanna_success else '❌ 失败'}")
    print(f"MySQL 连接: {'✅ 成功' if mysql_success else '❌ 失败'}")

    if vanna_success and mysql_success:
        print("\n🎉 所有测试通过！可以开始训练")
    else:
        print("\n⚠️ 存在问题，请根据上述信息进行修复")


if __name__ == "__main__":
    main()
