"""
彻底修复 Vanna 权限问题
解决 "The LLM is not allowed to see the data in your database" 错误
"""

import os
import sys
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.config import VANNA_CONFIG, VANNA_DB_CONFIG

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_vanna_instance_with_permissions():
    """创建带有正确权限的 Vanna 实例"""
    print("🔧 创建 Vanna 实例并设置权限...")
    
    try:
        # 检查是否有 Vanna API key
        vanna_api_key = VANNA_CONFIG.get("api_key")
        vanna_model = VANNA_CONFIG.get("model")
        
        if vanna_api_key and vanna_model:
            print("📡 使用 Vanna 云端模式")
            from vanna.remote import VannaDefault
            vn = VannaDefault(model=vanna_model, api_key=vanna_api_key)
        else:
            print("💻 使用 Vanna 本地模式")
            # 检查本地模式配置
            openai_api_key = os.getenv("OPENAI_API_KEY")
            base_url = os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1")
            
            if not openai_api_key:
                print("❌ 缺少 OPENAI_API_KEY 环境变量")
                print("💡 请设置环境变量或使用 setup_env.py 脚本")
                return None
            
            from vanna.openai import OpenAI_Chat
            from vanna.chromadb import ChromaDB_VectorStore
            
            class LocalContext_OpenAI(ChromaDB_VectorStore, OpenAI_Chat):
                def __init__(self, config=None):
                    ChromaDB_VectorStore.__init__(self, config=config)
                    OpenAI_Chat.__init__(self, config=config)
            
            openai_config = {
                "api_key": openai_api_key,
                "model": "gpt-3.5-turbo",
                "base_url": base_url
            }
            
            vn = LocalContext_OpenAI(config=openai_config)
        
        print("✅ Vanna 实例创建成功")
        
        # 连接数据库
        print("🔗 连接数据库...")
        vn.connect_to_mysql(**VANNA_DB_CONFIG)
        print("✅ 数据库连接成功")
        
        # 关键：设置权限的多种方法
        print("🔑 设置数据库内省权限...")
        
        # 方法1: 直接设置属性
        try:
            vn.allow_llm_to_see_data = True
            print("✅ 方法1成功: vn.allow_llm_to_see_data = True")
        except Exception as e:
            print(f"⚠️ 方法1失败: {e}")
        
        # 方法2: 通过 config 设置
        try:
            if hasattr(vn, 'config'):
                if hasattr(vn.config, 'allow_llm_to_see_data'):
                    vn.config.allow_llm_to_see_data = True
                    print("✅ 方法2成功: vn.config.allow_llm_to_see_data = True")
                elif isinstance(vn.config, dict):
                    vn.config['allow_llm_to_see_data'] = True
                    print("✅ 方法2成功: vn.config['allow_llm_to_see_data'] = True")
        except Exception as e:
            print(f"⚠️ 方法2失败: {e}")
        
        # 方法3: 通过 set_config 方法（如果存在）
        try:
            if hasattr(vn, 'set_config'):
                vn.set_config({'allow_llm_to_see_data': True})
                print("✅ 方法3成功: vn.set_config({'allow_llm_to_see_data': True})")
        except Exception as e:
            print(f"⚠️ 方法3失败: {e}")
        
        # 方法4: 直接修改内部属性
        try:
            for attr_name in ['_allow_llm_to_see_data', 'allow_llm_to_see_data']:
                if hasattr(vn, attr_name):
                    setattr(vn, attr_name, True)
                    print(f"✅ 方法4成功: {attr_name} = True")
        except Exception as e:
            print(f"⚠️ 方法4失败: {e}")
        
        # 验证权限设置
        print("\n🔍 验证权限设置...")
        permission_set = False
        
        if hasattr(vn, 'allow_llm_to_see_data'):
            print(f"  vn.allow_llm_to_see_data: {vn.allow_llm_to_see_data}")
            permission_set = vn.allow_llm_to_see_data
        
        if hasattr(vn, 'config') and vn.config:
            if hasattr(vn.config, 'allow_llm_to_see_data'):
                print(f"  vn.config.allow_llm_to_see_data: {vn.config.allow_llm_to_see_data}")
                permission_set = permission_set or vn.config.allow_llm_to_see_data
            elif isinstance(vn.config, dict) and 'allow_llm_to_see_data' in vn.config:
                print(f"  vn.config['allow_llm_to_see_data']: {vn.config['allow_llm_to_see_data']}")
                permission_set = permission_set or vn.config['allow_llm_to_see_data']
        
        if permission_set:
            print("✅ 权限设置验证成功")
        else:
            print("⚠️ 无法验证权限设置，但可能仍然有效")
        
        return vn
        
    except Exception as e:
        print(f"❌ Vanna 实例创建失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_vanna_permissions(vn):
    """测试 Vanna 权限和功能"""
    print("\n🧪 测试 Vanna 权限和功能...")
    
    try:
        # 测试1: 基本数据库查询
        print("📊 测试1: 基本数据库查询")
        tables = vn.run_sql("SHOW TABLES")
        print(f"✅ 查询成功，找到 {len(tables)} 个表")
        
        if len(tables) > 0:
            print("📋 数据库表:")
            for i, table in enumerate(tables.iloc[:3].values):
                print(f"  {i+1}. {table[0]}")
            if len(tables) > 3:
                print(f"  ... 还有 {len(tables) - 3} 个表")
        
        # 测试2: SQL 生成（关键测试）
        print("\n🤖 测试2: SQL 生成")
        test_questions = [
            "查询所有表的数量",
            "显示达人表的结构",
            "查询前5个达人"
        ]
        
        for question in test_questions:
            try:
                print(f"  问题: {question}")
                sql = vn.generate_sql(question)
                print(f"  生成的 SQL: {sql}")
                
                # 如果SQL不是错误消息，尝试执行
                if not sql.startswith("The LLM is not allowed"):
                    result = vn.run_sql(sql)
                    print(f"  执行结果: 成功 ({type(result)})")
                else:
                    print(f"  ❌ 权限错误: {sql}")
                    return False
                    
            except Exception as e:
                print(f"  ❌ 测试失败: {e}")
                return False
        
        print("✅ 所有测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def update_global_vanna_instance(vn):
    """更新全局 Vanna 实例"""
    print("\n🔄 更新全局 Vanna 实例...")
    
    try:
        import src.tools.database_tools as db_tools
        
        # 更新全局变量
        db_tools._global_vn_instance = vn
        db_tools._global_vanna_available = True
        
        print("✅ 全局 Vanna 实例更新成功")
        return True
        
    except Exception as e:
        print(f"❌ 更新全局实例失败: {e}")
        return False


def test_database_query_tool():
    """测试 DatabaseQueryTool"""
    print("\n🔧 测试 DatabaseQueryTool...")
    
    try:
        from src.tools.database_tools import DatabaseQueryTool
        
        tool = DatabaseQueryTool()
        
        # 测试简单查询
        test_question = "查询数据库中有多少个表"
        print(f"🔍 测试查询: {test_question}")
        
        result = tool._run(test_question)
        
        # 检查结果是否包含错误
        if "The LLM is not allowed" in result:
            print("❌ 权限错误仍然存在")
            return False
        elif "查询执行失败" in result:
            print("❌ 查询执行失败")
            print(f"错误详情: {result}")
            return False
        else:
            print("✅ 查询成功")
            print(f"结果预览: {result[:200]}...")
            return True
            
    except Exception as e:
        print(f"❌ DatabaseQueryTool 测试失败: {e}")
        return False


def main():
    """主修复函数"""
    print("🚨 Vanna 权限问题彻底修复工具")
    print("="*50)
    
    # 步骤1: 创建 Vanna 实例
    vn = create_vanna_instance_with_permissions()
    if not vn:
        print("❌ 无法创建 Vanna 实例，请检查环境配置")
        return
    
    # 步骤2: 测试权限
    if not test_vanna_permissions(vn):
        print("❌ Vanna 权限测试失败")
        return
    
    # 步骤3: 更新全局实例
    if not update_global_vanna_instance(vn):
        print("❌ 更新全局实例失败")
        return
    
    # 步骤4: 测试 DatabaseQueryTool
    if not test_database_query_tool():
        print("❌ DatabaseQueryTool 测试失败")
        return
    
    print("\n🎉 Vanna 权限问题修复成功！")
    print("\n💡 下一步:")
    print("1. 重启 Chainlit 应用: chainlit run app.py")
    print("2. 测试查询: '查询粉丝数最多的达人'")
    print("3. 享受增强的数据分析功能！")


if __name__ == "__main__":
    main()
