"""
测试核心增强功能的简化脚本
不依赖外部库，只测试数据格式化和增强逻辑
"""

import sys
import os
import json
import pandas as pd

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))


def test_field_mappings():
    """测试字段映射配置"""
    print("🧪 测试字段映射配置...")
    
    try:
        config_path = os.path.join("src", "config", "field_mappings.json")
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        print("✅ 配置文件加载成功")
        print(f"📊 字段映射表数量: {len(config.get('field_mappings', {}))}")
        print(f"🔧 数据增强器数量: {len(config.get('data_enhancers', {}))}")
        print(f"📋 查询模板数量: {len(config.get('query_templates', {}))}")
        
        # 显示字段映射示例
        field_mappings = config.get('field_mappings', {})
        for table_name, mappings in field_mappings.items():
            print(f"\n📋 表 {table_name} 的字段映射:")
            for orig, trans in list(mappings.items())[:5]:
                print(f"  {orig} -> {trans}")
            if len(mappings) > 5:
                print(f"  ... 还有 {len(mappings) - 5} 个字段")
        
        return config
        
    except Exception as e:
        print(f"❌ 配置文件加载失败: {e}")
        return None


def test_data_formatter_logic():
    """测试数据格式化逻辑"""
    print("\n🔄 测试数据格式化逻辑...")
    
    # 模拟数据
    sample_data = {
        'unique_id': ['user1', 'user2', 'user3'],
        'author_name': ['达人A', '达人B', '达人C'],
        'follower_count': [1234567, 987654, 543210],
        'heart_count': [5678901, 3456789, 1234567],
        'is_verified': [1, 0, 1],
        'register_time': [1640995200, 1641081600, 1641168000]  # 时间戳
    }
    
    df = pd.DataFrame(sample_data)
    print("📊 原始数据:")
    print(df.to_string(index=False))
    
    # 模拟字段映射
    field_mapping = {
        'unique_id': '用户名',
        'author_name': '达人昵称', 
        'follower_count': '粉丝数',
        'heart_count': '总获赞数',
        'is_verified': '认证状态',
        'register_time': '注册时间'
    }
    
    # 应用字段映射
    df_mapped = df.rename(columns=field_mapping)
    
    # 格式化数值
    df_mapped['粉丝数'] = df_mapped['粉丝数'].apply(lambda x: f"{x:,}")
    df_mapped['总获赞数'] = df_mapped['总获赞数'].apply(lambda x: f"{x:,}")
    df_mapped['认证状态'] = df_mapped['认证状态'].apply(lambda x: "已认证" if x == 1 else "未认证")
    
    # 格式化时间
    from datetime import datetime
    df_mapped['注册时间'] = df_mapped['注册时间'].apply(
        lambda x: datetime.fromtimestamp(x).strftime("%Y-%m-%d %H:%M:%S")
    )
    
    print("\n✨ 格式化后的数据:")
    print(df_mapped.to_string(index=False))
    
    # 计算汇总统计
    original_follower = df['follower_count']
    print(f"\n📈 数据汇总:")
    print(f"  粉丝数总计: {original_follower.sum():,}")
    print(f"  粉丝数平均: {original_follower.mean():,.0f}")
    print(f"  粉丝数最大: {original_follower.max():,}")
    print(f"  粉丝数最小: {original_follower.min():,}")
    
    print("✅ 数据格式化逻辑测试完成")


def test_query_intent_analysis():
    """测试查询意图分析逻辑"""
    print("\n🧠 测试查询意图分析...")
    
    # 查询模式定义
    query_patterns = {
        "达人排行": [
            r"粉丝.*最多", r"粉丝.*排行", r"粉丝.*前\d+", r"粉丝.*top\d*",
            r"获赞.*最多", r"获赞.*排行", r"点赞.*最多", r"点赞.*排行",
            r"达人.*排行", r"博主.*排行", r"用户.*排行"
        ],
        "作品排行": [
            r"播放.*最多", r"播放.*排行", r"播放.*前\d+", r"播放.*top\d*",
            r"热门.*作品", r"热门.*视频", r"爆款.*作品", r"爆款.*视频"
        ],
        "互动分析": [
            r"互动率", r"参与度", r"点赞率", r"评论率", r"分享率",
            r"互动.*分析", r"互动.*统计", r"互动.*数据"
        ]
    }
    
    # 测试查询
    test_queries = [
        "查询粉丝数最多的10个达人",
        "分析播放量超过100万的视频", 
        "统计各地区达人的互动率",
        "查看热门作品排行",
        "分析点赞率最高的内容"
    ]
    
    import re
    
    for query in test_queries:
        print(f"\n🔍 查询: {query}")
        query_lower = query.lower()
        matched_intents = []
        
        for intent, patterns in query_patterns.items():
            for pattern in patterns:
                if re.search(pattern, query_lower):
                    matched_intents.append(intent)
                    break
        
        if matched_intents:
            print(f"  识别意图: {matched_intents[0]}")
        else:
            print(f"  识别意图: 通用查询")
    
    print("✅ 查询意图分析测试完成")


def test_enhancement_suggestions():
    """测试字段增强建议"""
    print("\n💡 测试字段增强建议...")
    
    # 增强配置
    enhancement_config = {
        "达人排行": {
            "auto_fields": ["unique_id", "author_name", "follower_count", "heart_count", "video_count", "region", "is_verified"],
            "description": "自动补充达人基本信息"
        },
        "作品排行": {
            "auto_fields": ["title", "unique_id", "author_name", "play_count", "like_count", "comment_count", "share_count", "publish_time"],
            "description": "自动补充作品基本信息"
        },
        "互动分析": {
            "auto_fields": ["title", "unique_id", "author_name", "play_count", "like_count", "comment_count", "share_count", "collect_count"],
            "calculated_fields": ["engagement_rate", "like_rate"],
            "description": "自动计算互动指标"
        }
    }
    
    test_cases = [
        ("达人排行", ["unique_id", "follower_count"]),
        ("作品排行", ["title", "play_count"]),
        ("互动分析", ["play_count", "like_count"])
    ]
    
    for intent, current_fields in test_cases:
        print(f"\n📊 查询意图: {intent}")
        print(f"  当前字段: {current_fields}")
        
        if intent in enhancement_config:
            config = enhancement_config[intent]
            auto_fields = config["auto_fields"]
            suggested_fields = [field for field in auto_fields if field not in current_fields]
            
            print(f"  建议补充: {suggested_fields}")
            print(f"  增强说明: {config['description']}")
            
            if "calculated_fields" in config:
                print(f"  计算字段: {config['calculated_fields']}")
    
    print("✅ 字段增强建议测试完成")


def test_interaction_rate_calculation():
    """测试互动率计算"""
    print("\n📊 测试互动率计算...")
    
    # 模拟作品数据
    works_data = {
        'title': ['作品A', '作品B', '作品C'],
        'play_count': [1000000, 500000, 2000000],
        'like_count': [50000, 25000, 80000],
        'comment_count': [5000, 2500, 8000],
        'share_count': [2000, 1000, 3000]
    }
    
    df = pd.DataFrame(works_data)
    
    # 计算互动率
    df['互动率(%)'] = ((df['like_count'] + df['comment_count'] + df['share_count']) / df['play_count'] * 100).round(2)
    df['点赞率(%)'] = (df['like_count'] / df['play_count'] * 100).round(2)
    
    # 格式化显示
    display_df = df.copy()
    display_df['播放量'] = display_df['play_count'].apply(lambda x: f"{x:,}")
    display_df['点赞数'] = display_df['like_count'].apply(lambda x: f"{x:,}")
    display_df['评论数'] = display_df['comment_count'].apply(lambda x: f"{x:,}")
    display_df['分享数'] = display_df['share_count'].apply(lambda x: f"{x:,}")
    
    result_df = display_df[['title', '播放量', '点赞数', '评论数', '分享数', '互动率(%)', '点赞率(%)']]
    result_df.columns = ['作品标题', '播放量', '点赞数', '评论数', '分享数', '互动率(%)', '点赞率(%)']
    
    print("📈 互动率计算结果:")
    print(result_df.to_string(index=False))
    
    print("✅ 互动率计算测试完成")


def main():
    """主测试函数"""
    print("🎯 TikTok 数据分析增强功能核心测试")
    print("="*60)
    
    # 测试配置加载
    config = test_field_mappings()
    
    if config:
        # 测试数据格式化
        test_data_formatter_logic()
        
        # 测试查询意图分析
        test_query_intent_analysis()
        
        # 测试字段增强建议
        test_enhancement_suggestions()
        
        # 测试互动率计算
        test_interaction_rate_calculation()
        
        print("\n🎉 所有核心功能测试完成！")
        print("\n📝 功能总结:")
        print("✅ 字段映射配置 - 数据库字段自动转换为中文说明")
        print("✅ 查询意图识别 - 智能识别用户查询类型")
        print("✅ 数据格式化 - 数值、时间、布尔值友好格式化")
        print("✅ 字段自动补充 - 根据查询意图补充相关字段")
        print("✅ 衍生指标计算 - 自动计算互动率、点赞率等")
        print("✅ 结构化返回 - 统一的数据返回格式")
        
        print("\n💡 实际效果:")
        print("- 用户查询'粉丝数最多的达人' → 自动补充用户名、昵称、获赞数等完整信息")
        print("- 数据库字段'follower_count' → 显示为'粉丝数'")
        print("- 数值1234567 → 显示为'1,234,567'")
        print("- 布尔值1 → 显示为'已认证'")
        print("- 自动计算互动率 = (点赞+评论+分享)/播放量 * 100%")
    
    else:
        print("❌ 配置文件加载失败，无法继续测试")


if __name__ == "__main__":
    main()
