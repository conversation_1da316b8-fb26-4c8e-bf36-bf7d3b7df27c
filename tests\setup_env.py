"""
环境变量设置助手
帮助快速设置 TikTok 数据分析系统所需的环境变量
"""

import os


def check_env_file():
    """检查 .env 文件是否存在"""
    env_path = ".env"
    if os.path.exists(env_path):
        print("✅ 找到 .env 文件")
        return True
    else:
        print("❌ 未找到 .env 文件")
        return False


def read_env_file():
    """读取现有的 .env 文件"""
    env_vars = {}
    try:
        with open(".env", "r", encoding="utf-8") as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith("#") and "=" in line:
                    key, value = line.split("=", 1)
                    env_vars[key.strip()] = value.strip()
        return env_vars
    except Exception as e:
        print(f"读取 .env 文件失败: {e}")
        return {}


def create_env_template():
    """创建 .env 文件模板"""
    template = """# TikTok 数据分析系统环境变量配置

# 数据库配置
MYSQL_HOST=localhost
MYSQL_USER=root
MYSQL_PASSWORD=your_password_here
MYSQL_DATABASE=tiktok_data
MYSQL_PORT=3306
MYSQL_CONNECTION_TIMEOUT=60
MYSQL_POOL_SIZE=5

# OpenAI 配置 (本地模式)
OPENAI_API_KEY=sk-your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1

# Vanna 配置 (云端模式，可选)
VANNA_API_KEY=vn-your_vanna_api_key_here
VANNA_MODEL=your_vanna_model_name

# 其他配置
LOG_LEVEL=INFO
"""
    
    try:
        with open(".env", "w", encoding="utf-8") as f:
            f.write(template)
        print("✅ 已创建 .env 文件模板")
        return True
    except Exception as e:
        print(f"❌ 创建 .env 文件失败: {e}")
        return False


def interactive_setup():
    """交互式设置环境变量"""
    print("\n🔧 交互式环境变量设置")
    print("请输入以下配置信息（按回车跳过）:")
    
    configs = {}
    
    # 数据库配置
    print("\n📊 数据库配置:")
    configs["MYSQL_HOST"] = input("MySQL 主机地址 [localhost]: ").strip() or "localhost"
    configs["MYSQL_USER"] = input("MySQL 用户名 [root]: ").strip() or "root"
    configs["MYSQL_PASSWORD"] = input("MySQL 密码: ").strip()
    configs["MYSQL_DATABASE"] = input("数据库名 [tiktok_data]: ").strip() or "tiktok_data"
    configs["MYSQL_PORT"] = input("MySQL 端口 [3306]: ").strip() or "3306"
    
    # OpenAI 配置
    print("\n🤖 OpenAI 配置:")
    configs["OPENAI_API_KEY"] = input("OpenAI API Key: ").strip()
    configs["OPENAI_BASE_URL"] = input("OpenAI Base URL [https://api.openai.com/v1]: ").strip() or "https://api.openai.com/v1"
    
    # Vanna 配置
    print("\n☁️ Vanna 云端配置 (可选):")
    configs["VANNA_API_KEY"] = input("Vanna API Key (可选): ").strip()
    configs["VANNA_MODEL"] = input("Vanna Model Name (可选): ").strip()
    
    return configs


def write_env_file(configs):
    """写入 .env 文件"""
    try:
        with open(".env", "w", encoding="utf-8") as f:
            f.write("# TikTok 数据分析系统环境变量配置\n")
            f.write("# 由 setup_env.py 自动生成\n\n")
            
            f.write("# 数据库配置\n")
            f.write(f"MYSQL_HOST={configs.get('MYSQL_HOST', 'localhost')}\n")
            f.write(f"MYSQL_USER={configs.get('MYSQL_USER', 'root')}\n")
            f.write(f"MYSQL_PASSWORD={configs.get('MYSQL_PASSWORD', '')}\n")
            f.write(f"MYSQL_DATABASE={configs.get('MYSQL_DATABASE', 'tiktok_data')}\n")
            f.write(f"MYSQL_PORT={configs.get('MYSQL_PORT', '3306')}\n")
            f.write("MYSQL_CONNECTION_TIMEOUT=60\n")
            f.write("MYSQL_POOL_SIZE=5\n\n")
            
            f.write("# OpenAI 配置\n")
            f.write(f"OPENAI_API_KEY={configs.get('OPENAI_API_KEY', '')}\n")
            f.write(f"OPENAI_BASE_URL={configs.get('OPENAI_BASE_URL', 'https://api.openai.com/v1')}\n\n")
            
            if configs.get('VANNA_API_KEY') or configs.get('VANNA_MODEL'):
                f.write("# Vanna 云端配置\n")
                f.write(f"VANNA_API_KEY={configs.get('VANNA_API_KEY', '')}\n")
                f.write(f"VANNA_MODEL={configs.get('VANNA_MODEL', '')}\n\n")
            
            f.write("# 其他配置\n")
            f.write("LOG_LEVEL=INFO\n")
        
        print("✅ .env 文件已保存")
        return True
    except Exception as e:
        print(f"❌ 保存 .env 文件失败: {e}")
        return False


def validate_config():
    """验证配置"""
    print("\n🔍 验证配置...")
    
    required_vars = ["MYSQL_HOST", "MYSQL_USER", "MYSQL_PASSWORD", "MYSQL_DATABASE", "OPENAI_API_KEY"]
    missing_vars = []
    
    for var in required_vars:
        value = os.getenv(var)
        if not value:
            missing_vars.append(var)
        else:
            if 'password' in var.lower() or 'key' in var.lower():
                print(f"  ✅ {var}: {'*' * min(len(value), 8)}")
            else:
                print(f"  ✅ {var}: {value}")
    
    if missing_vars:
        print(f"\n❌ 缺少必需的环境变量: {', '.join(missing_vars)}")
        return False
    else:
        print("\n✅ 所有必需的环境变量都已设置")
        return True


def main():
    """主函数"""
    print("🚀 TikTok 数据分析系统环境变量设置助手")
    print("="*50)
    
    # 检查现有 .env 文件
    has_env = check_env_file()
    
    if has_env:
        print("\n📋 当前 .env 文件内容:")
        env_vars = read_env_file()
        for key, value in env_vars.items():
            if 'password' in key.lower() or 'key' in key.lower():
                print(f"  {key}={'*' * min(len(value), 8) if value else '(空)'}")
            else:
                print(f"  {key}={value}")
        
        choice = input("\n是否要重新配置? [y/N]: ").strip().lower()
        if choice not in ['y', 'yes', '是']:
            print("保持现有配置")
            return
    
    print("\n选择设置方式:")
    print("1. 交互式设置")
    print("2. 创建模板文件")
    
    choice = input("请选择 [1/2]: ").strip()
    
    if choice == "1":
        configs = interactive_setup()
        if write_env_file(configs):
            print("\n🎉 环境变量设置完成！")
            
            # 重新加载环境变量
            from dotenv import load_dotenv
            load_dotenv()
            
            # 验证配置
            validate_config()
            
            print("\n💡 下一步:")
            print("1. 重启终端或应用")
            print("2. 运行: python train_vanna.py (如果是新安装)")
            print("3. 运行: chainlit run app.py")
        
    elif choice == "2":
        if create_env_template():
            print("\n📝 已创建 .env 文件模板")
            print("请编辑 .env 文件，填入正确的配置值")
            print("然后重启应用")
    
    else:
        print("无效选择")


if __name__ == "__main__":
    main()
