"""
修复 Vanna 权限配置问题
解决 "The LLM is not allowed to see the data in your database" 错误
"""

import os
import sys
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.config import VANNA_CONFIG, VANNA_DB_CONFIG

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def check_vanna_config():
    """检查 Vanna 配置"""
    print("🔍 检查 Vanna 配置...")
    
    print(f"📋 Vanna 配置:")
    for key, value in VANNA_CONFIG.items():
        if 'key' in key.lower() or 'password' in key.lower():
            print(f"  {key}: {'*' * len(str(value)) if value else 'None'}")
        else:
            print(f"  {key}: {value}")
    
    print(f"\n📋 Vanna 数据库配置:")
    for key, value in VANNA_DB_CONFIG.items():
        if 'password' in key.lower():
            print(f"  {key}: {'*' * len(str(value)) if value else 'None'}")
        else:
            print(f"  {key}: {value}")


def fix_vanna_permissions():
    """修复 Vanna 权限问题"""
    print("\n🔧 修复 Vanna 权限配置...")
    
    try:
        # 检查是否有 Vanna API key
        vanna_api_key = VANNA_CONFIG.get("api_key")
        vanna_model = VANNA_CONFIG.get("model")
        
        if vanna_api_key and vanna_model:
            print("📡 使用 Vanna 云端模式")
            from vanna.remote import VannaDefault
            vn = VannaDefault(model=vanna_model, api_key=vanna_api_key)
        else:
            print("💻 使用 Vanna 本地模式")
            # 检查本地模式配置
            openai_api_key = os.getenv("OPENAI_API_KEY")
            base_url = os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1")
            
            if not openai_api_key:
                print("❌ 缺少 OPENAI_API_KEY 环境变量")
                return False
            
            from vanna.openai import OpenAI_Chat
            from vanna.chromadb import ChromaDB_VectorStore
            
            class LocalContext_OpenAI(ChromaDB_VectorStore, OpenAI_Chat):
                def __init__(self, config=None):
                    ChromaDB_VectorStore.__init__(self, config=config)
                    OpenAI_Chat.__init__(self, config=config)
            
            openai_config = {
                "api_key": openai_api_key,
                "model": "gpt-3.5-turbo",
                "base_url": base_url
            }
            
            vn = LocalContext_OpenAI(config=openai_config)
        
        print("✅ Vanna 实例创建成功")
        
        # 连接数据库
        print("🔗 连接数据库...")
        vn.connect_to_mysql(**VANNA_DB_CONFIG)
        print("✅ 数据库连接成功")
        
        # 设置权限 - 多种方式确保生效
        print("🔑 设置数据库内省权限...")
        
        # 方式1: 直接设置属性
        vn.allow_llm_to_see_data = True
        print("✅ 设置 allow_llm_to_see_data = True")
        
        # 方式2: 如果有config属性，也设置一下
        if hasattr(vn, 'config') and vn.config is not None:
            if hasattr(vn.config, 'allow_llm_to_see_data'):
                vn.config.allow_llm_to_see_data = True
                print("✅ 设置 config.allow_llm_to_see_data = True")
        
        # 方式3: 如果是字典类型的config
        if hasattr(vn, 'config') and isinstance(vn.config, dict):
            vn.config['allow_llm_to_see_data'] = True
            print("✅ 设置 config['allow_llm_to_see_data'] = True")
        
        # 验证设置
        print("\n🔍 验证权限设置...")
        if hasattr(vn, 'allow_llm_to_see_data'):
            print(f"  allow_llm_to_see_data: {vn.allow_llm_to_see_data}")
        else:
            print("  ⚠️ 无法访问 allow_llm_to_see_data 属性")
        
        # 测试基本查询
        print("\n🧪 测试基本数据库查询...")
        try:
            tables = vn.run_sql("SHOW TABLES")
            print(f"✅ 查询成功，找到 {len(tables)} 个表")
            
            if len(tables) > 0:
                print("📋 数据库表列表:")
                for i, table in enumerate(tables.iloc[:5].values):  # 只显示前5个
                    print(f"  {i+1}. {table[0]}")
                if len(tables) > 5:
                    print(f"  ... 还有 {len(tables) - 5} 个表")
        except Exception as e:
            print(f"❌ 基本查询失败: {e}")
            return False
        
        # 测试 SQL 生成
        print("\n🤖 测试 SQL 生成...")
        try:
            test_question = "查询所有表的数量"
            sql = vn.generate_sql(test_question)
            print(f"✅ SQL 生成成功:")
            print(f"  问题: {test_question}")
            print(f"  生成的 SQL: {sql}")
            
            # 执行生成的 SQL
            result = vn.run_sql(sql)
            print(f"✅ SQL 执行成功，结果类型: {type(result)}")
            
        except Exception as e:
            print(f"❌ SQL 生成或执行失败: {e}")
            print("💡 这可能是因为 Vanna 模型还没有训练，或者权限设置仍有问题")
            return False
        
        print("\n🎉 Vanna 权限配置修复成功！")
        return True
        
    except Exception as e:
        print(f"❌ Vanna 权限修复失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def provide_solutions():
    """提供解决方案建议"""
    print("\n💡 解决方案建议:")
    print("\n1. 🔑 **确保权限设置**")
    print("   在使用 Vanna 前，确保设置:")
    print("   ```python")
    print("   vn.allow_llm_to_see_data = True")
    print("   ```")
    
    print("\n2. 📚 **重新训练模型**")
    print("   如果是新的 Vanna 实例，需要先训练:")
    print("   ```bash")
    print("   python train_vanna.py")
    print("   ```")
    
    print("\n3. 🔧 **检查环境变量**")
    print("   确保以下环境变量正确设置:")
    print("   - OPENAI_API_KEY (本地模式)")
    print("   - OPENAI_BASE_URL (可选)")
    print("   - VANNA_API_KEY (云端模式)")
    print("   - VANNA_MODEL (云端模式)")
    
    print("\n4. 🗄️ **检查数据库连接**")
    print("   确保数据库配置正确:")
    print("   - MYSQL_HOST")
    print("   - MYSQL_USER") 
    print("   - MYSQL_PASSWORD")
    print("   - MYSQL_DATABASE")
    
    print("\n5. 🔄 **重启应用**")
    print("   修改配置后，重启 Chainlit 应用:")
    print("   ```bash")
    print("   chainlit run app.py")
    print("   ```")


def main():
    """主函数"""
    print("🔧 Vanna 权限配置修复工具")
    print("="*50)
    
    # 检查配置
    check_vanna_config()
    
    # 尝试修复权限
    success = fix_vanna_permissions()
    
    if success:
        print("\n✅ 修复成功！现在可以正常使用数据库查询功能了。")
    else:
        print("\n❌ 修复失败，请查看上面的错误信息。")
        provide_solutions()


if __name__ == "__main__":
    main()
