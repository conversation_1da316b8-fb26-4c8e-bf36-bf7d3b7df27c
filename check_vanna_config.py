"""
检查 Vanna 配置的简单脚本
"""

import os
from dotenv import load_dotenv


def check_vanna_config():
    """检查 Vanna 训练所需的配置"""
    print("🔍 检查 Vanna 训练配置")
    print("=" * 50)

    # 加载环境变量
    load_dotenv()

    # 检查 API 密钥
    openai_key = os.getenv("OPENAI_API_KEY")
    openai_base_url = os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1")
    dashscope_key = os.getenv("DASHSCOPE_API_KEY")

    print("🔑 API 密钥检查:")
    if openai_key and openai_key != "your-openai-api-key-here":
        print(f"  ✅ OPENAI_API_KEY: 已配置 (sk-...{openai_key[-4:]})")
        print(f"  🌐 OPENAI_BASE_URL: {openai_base_url}")
        vanna_ready = True
    else:
        print("  ❌ OPENAI_API_KEY: 未配置或使用默认值")
        print(f"  🌐 OPENAI_BASE_URL: {openai_base_url} (默认)")
        vanna_ready = False

    if dashscope_key:
        print(f"  ✅ DASHSCOPE_API_KEY: 已配置 (sk-...{dashscope_key[-4:]})")
        app_ready = True
    else:
        print("  ❌ DASHSCOPE_API_KEY: 未配置")
        app_ready = False

    # 检查数据库配置
    print("\n🗄️ 数据库配置检查:")
    db_config = {
        "MYSQL_HOST": os.getenv("MYSQL_HOST"),
        "MYSQL_USER": os.getenv("MYSQL_USER"),
        "MYSQL_PASSWORD": os.getenv("MYSQL_PASSWORD"),
        "MYSQL_DATABASE": os.getenv("MYSQL_DATABASE"),
    }

    db_ready = True
    for key, value in db_config.items():
        if value and value != "your_password":
            if key == "MYSQL_PASSWORD":
                print(f"  ✅ {key}: 已配置")
            else:
                print(f"  ✅ {key}: {value}")
        else:
            print(f"  ❌ {key}: 未配置或使用默认值")
            db_ready = False

    # 总结
    print("\n📊 配置状态总结:")
    print(f"  Vanna 训练就绪: {'✅ 是' if vanna_ready and db_ready else '❌ 否'}")
    print(f"  应用运行就绪: {'✅ 是' if app_ready and db_ready else '❌ 否'}")

    # 给出建议
    print("\n💡 建议:")
    if not vanna_ready:
        print("  1. 配置 OPENAI_API_KEY 以启用 Vanna 训练功能")
        print("     - 访问 https://platform.openai.com/ 获取 API 密钥")
        print('     - 在 .env 文件中设置: OPENAI_API_KEY="sk-your-key"')
        print(
            '     - 可选：设置自定义 base_url: OPENAI_BASE_URL="https://your-proxy.com/v1"'
        )

    if not app_ready:
        print("  2. 配置 DASHSCOPE_API_KEY 以使用通义千问")
        print("     - 访问 https://bailian.console.aliyun.com/ 获取 API 密钥")
        print('     - 在 .env 文件中设置: DASHSCOPE_API_KEY="sk-your-key"')

    if not db_ready:
        print("  3. 配置数据库连接信息")
        print("     - 确保数据库服务正在运行")
        print("     - 在 .env 文件中设置正确的数据库参数")

    if vanna_ready and db_ready:
        print("  🎉 配置完整！可以开始训练:")
        print("     python train_vanna.py --config training_config.json --verbose")

    if app_ready and db_ready:
        print("  🚀 可以启动应用:")
        print("     python run.py")


if __name__ == "__main__":
    check_vanna_config()
